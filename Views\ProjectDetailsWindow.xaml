<Window x:Class="FinancialTracker.ProjectDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        xmlns:converters="clr-namespace:FinancialTracker.Converters"
        Title="Project Details"
        Height="950"
        Width="1600"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="#F7F7F7">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <local:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>

        <!-- Payment Status Color Converter -->
        <converters:PaymentStatusToColorConverter x:Key="PaymentStatusToColorConverter"/>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header - SAP Style -->
        <Border Grid.Row="0" Background="#4A5568" Padding="24,20" CornerRadius="6" Margin="0,0,0,24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="36" Height="36" Margin="0,0,16,0" Foreground="White"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock x:Name="ProjectNameText" Text="Project Details" FontSize="24" FontWeight="SemiBold" Foreground="White"/>
                        <TextBlock x:Name="ProjectStatusText" Text="Active" FontSize="13" Foreground="#E5E7EB" Opacity="0.9" Margin="0,4,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Navigation Tabs -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Button x:Name="SummaryTabBtn" Style="{StaticResource MaterialDesignRaisedButton}"
                            Margin="8,0" Padding="20,10" Click="SummaryTabBtn_Click"
                            Background="White" Foreground="#374151" FontWeight="Medium">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Width="18" Height="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="Financial Summary" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="InvoicesTabBtn" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="8,0" Padding="20,10" Click="InvoicesTabBtn_Click"
                            BorderBrush="White" Foreground="White" FontWeight="Medium">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="Invoices" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="CommitmentsTabBtn" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="8,0" Padding="20,10" Click="CommitmentsTabBtn_Click"
                            BorderBrush="White" Foreground="White" FontWeight="Medium">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Handshake" Width="18" Height="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="Commitments" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">

                    <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="Refresh Data" Margin="12,0" Click="RefreshButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" Width="22" Height="22" Foreground="White"/>
                    </Button>
                    <Button x:Name="ViewPOButton" Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="View PO File" Margin="12,0" Click="ViewPOFileButton_Click"
                            Visibility="Collapsed">
                        <materialDesign:PackIcon Kind="FileDocument" Width="22" Height="22" Foreground="White"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White" Foreground="#374151" Margin="12,0" Padding="20,10"
                            Click="EditProjectButton_Click" FontWeight="Medium">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="Edit Project" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>



        <!-- Main Content -->
        <Grid Grid.Row="2" Margin="0">
            <!-- Financial Summary Content -->
            <ScrollViewer x:Name="FinancialSummaryContent" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="0,0,12,0" CanContentScroll="False">
                <StackPanel>
                    <!-- Project Overview Card -->
                    <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Foreground="#4A5568" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <TextBlock Text="Project Overview" FontSize="18" FontWeight="SemiBold" Foreground="#374151" VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Basic Info -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                    <TextBlock Text="Project Name" FontWeight="Medium" Foreground="#6B7280" FontSize="12"/>
                                    <TextBlock x:Name="ProjectNameDetail" Text="" FontSize="14" FontWeight="SemiBold" Foreground="#374151" Margin="0,4,0,0"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1" Margin="0,0,12,0">
                                    <TextBlock Text="Status" FontWeight="Medium" Foreground="#6B7280" FontSize="12"/>
                                    <TextBlock x:Name="ProjectStatusDetail" Text="" FontSize="14" FontWeight="Medium" Foreground="#374151" Margin="0,4,0,0"/>
                                </StackPanel>
                                <StackPanel Grid.Column="2" Margin="0,0,12,0">
                                    <TextBlock Text="PO Date" FontWeight="Medium" Foreground="#6B7280" FontSize="12"/>
                                    <TextBlock x:Name="ProjectPODateDetail" Text="" FontSize="14" Foreground="#374151" Margin="0,4,0,0"/>
                                </StackPanel>
                                <StackPanel Grid.Column="3">
                                    <TextBlock Text="Description" FontWeight="Medium" Foreground="#6B7280" FontSize="12"/>
                                    <TextBlock x:Name="ProjectDescriptionDetail" Text="" FontSize="14" Foreground="#374151" TextWrapping="Wrap" Margin="0,4,0,0"/>

                                    <!-- Buttons Row -->
                                    <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                        <Button x:Name="ManageSitesButton" Content="Manage Sites"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                FontSize="11" Padding="12,6" Margin="0,0,8,0"
                                                BorderBrush="#4A5568" Foreground="#4A5568" FontWeight="Medium"
                                                Click="ManageSitesButton_Click">
                                            <Button.ToolTip>
                                                <ToolTip Content="Edit site names and settings"/>
                                            </Button.ToolTip>
                                        </Button>

                                        <Button x:Name="ProjectFilesButton" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                FontSize="11" Padding="12,6"
                                                BorderBrush="#4A5568" Foreground="#4A5568" FontWeight="Medium"
                                                Click="ProjectFilesButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FolderOpen" Width="14" Height="14" VerticalAlignment="Center" Margin="0,0,6,0"/>
                                                <TextBlock Text="Files" VerticalAlignment="Center"/>
                                            </StackPanel>
                                            <Button.ToolTip>
                                                <ToolTip Content="View project files"/>
                                            </Button.ToolTip>
                                        </Button>
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                            <!-- Enhanced Financial Summary -->
                            <materialDesign:Card Margin="0,16,0,0" Padding="0" materialDesign:ShadowAssist.ShadowDepth="Depth2">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <!-- Modern Header -->
                                    <Border Grid.Row="0" Background="#1565C0" Padding="20,16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                                <TextBlock Text="Financial Summary" FontWeight="SemiBold" FontSize="20" Foreground="White" VerticalAlignment="Center"/>
                                            </StackPanel>
                                            <materialDesign:PackIcon Grid.Column="1" Kind="TrendingUp" Width="28" Height="28" Foreground="White" Opacity="0.7"/>
                                        </Grid>
                                    </Border>

                                    <!-- Enhanced Content -->
                                    <Grid Grid.Row="1" Margin="20">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Total PO Value Card -->
                                        <Border Grid.Column="0" Background="#E8F5E8" CornerRadius="8" Padding="16" Margin="0,0,8,0">
                                            <StackPanel>
                                                <Grid Margin="0,0,0,8">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Column="0" Text="Total PO Value" FontWeight="Medium" Foreground="#2E7D32" FontSize="13"/>
                                                    <materialDesign:PackIcon Grid.Column="1" Kind="CurrencyUsd" Width="18" Height="18" Foreground="#2E7D32"/>
                                                </Grid>
                                                <TextBlock x:Name="POAmountDetail" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="#1B5E20"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Total Spent Card -->
                                        <Border Grid.Column="1" Background="#FFEBEE" CornerRadius="8" Padding="16" Margin="4,0">
                                            <StackPanel>
                                                <Grid Margin="0,0,0,8">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Column="0" Text="Total Spent" FontWeight="Medium" Foreground="#C62828" FontSize="13"/>
                                                    <materialDesign:PackIcon Grid.Column="1" Kind="TrendingDown" Width="18" Height="18" Foreground="#C62828"/>
                                                </Grid>
                                                <TextBlock x:Name="SpentFromPODetail" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="#B71C1C"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Remaining Card -->
                                        <Border Grid.Column="2" Background="#E3F2FD" CornerRadius="8" Padding="16" Margin="4,0">
                                            <StackPanel>
                                                <Grid Margin="0,0,0,8">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Column="0" Text="Remaining" FontWeight="Medium" Foreground="#1565C0" FontSize="13"/>
                                                    <materialDesign:PackIcon Grid.Column="1" Kind="Wallet" Width="18" Height="18" Foreground="#1565C0"/>
                                                </Grid>
                                                <TextBlock x:Name="RemainingFromPODetail" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="#0D47A1"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- PO File Card -->
                                        <Border Grid.Column="3" Background="#F3E5F5" CornerRadius="8" Padding="16" Margin="8,0,0,0">
                                            <StackPanel>
                                                <Grid Margin="0,0,0,8">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Column="0" Text="PO File" FontWeight="Medium" Foreground="#7B1FA2" FontSize="13"/>
                                                    <materialDesign:PackIcon Grid.Column="1" Kind="FileDocument" Width="18" Height="18" Foreground="#7B1FA2"/>
                                                </Grid>
                                                <Button x:Name="ViewPOFileButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                                        FontSize="12" Padding="12,8" Background="#7B1FA2" Foreground="White"
                                                        Click="ViewPOFileButton_Click" Visibility="Collapsed">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="Eye" Width="14" Height="14" VerticalAlignment="Center" Margin="0,0,6,0"/>
                                                        <TextBlock Text="View PO" VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Button>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                        </StackPanel>
                    </Border>







                            <!-- Equipment Financial Summary - Will show either combined or split based on project settings -->
                            <StackPanel x:Name="TasksFinancialSummaryPanel">

                                <!-- Combined Equipment Summary (when not split) -->
                                <materialDesign:Card x:Name="CombinedTasksCard" Margin="0,0,0,8" Background="#F8F9FA" Padding="8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="Cog" Width="14" Height="14" Foreground="#1976D2" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="Total Equipment:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="TasksTotalValueText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#1976D2" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="TrendingUp" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="Equipment Invoiced:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="TasksInvoicedText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="TrendingDown" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="Equipment Left:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="TasksRemainingText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </materialDesign:Card>

                                <!-- Hardware Equipment Summary (when split) -->
                                <materialDesign:Card x:Name="HardwareTasksCard" Margin="0,0,0,4" Background="#FFF3E0" Padding="8" Visibility="Collapsed">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="Memory" Width="14" Height="14" Foreground="#E65100" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="Total Hardware:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="HardwareTotalValueText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#E65100" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="TrendingUp" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="HW Invoiced:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="HardwareInvoicedText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="TrendingDown" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="HW Left:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="HardwareRemainingText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </materialDesign:Card>

                                <!-- Software Equipment Summary (when split) -->
                                <materialDesign:Card x:Name="SoftwareTasksCard" Margin="0,0,0,8" Background="#E8F5E8" Padding="8" Visibility="Collapsed">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="Application" Width="14" Height="14" Foreground="#2E7D32" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="Total Software:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="SoftwareTotalValueText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#2E7D32" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="TrendingUp" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="SW Invoiced:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="SoftwareInvoicedText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="TrendingDown" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="SW Left:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                            <TextBlock x:Name="SoftwareRemainingText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </materialDesign:Card>

                            </StackPanel>

                            <!-- Services Financial Summary - Compact Style -->
                            <materialDesign:Card Margin="0,0,0,16" Background="#F8F9FA" Padding="8">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="HandHeart" Width="14" Height="14" Foreground="#1976D2" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Total Services:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="ServicesTotalValueText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#1976D2" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="TrendingUp" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Services Invoiced:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="ServicesInvoicedText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="TrendingDown" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Services Left:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="ServicesRemainingText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- Spare Parts Financial Summary -->
                            <materialDesign:Card x:Name="SparePartsCard" Margin="0,0,0,8" Background="#FFF8E1" Padding="8" Visibility="Collapsed">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="Wrench" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Total Spare Parts:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="SparePartsTotalText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="TrendingUp" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Spare Parts Invoiced:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="SparePartsInvoicedText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="TrendingDown" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Spare Parts Left:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="SparePartsRemainingText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- Extra Financial Summary -->
                            <materialDesign:Card x:Name="ExtraCard" Margin="0,0,0,8" Background="#FEF3C7" Padding="8" Visibility="Collapsed">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="StarCircle" Width="14" Height="14" Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock x:Name="ExtraTotalLabel" Text="Total Extra:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="ExtraTotalText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F59E0B" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="TrendingUp" Width="14" Height="14" Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock x:Name="ExtraInvoicedLabel" Text="Extra Invoiced:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="ExtraInvoicedText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F59E0B" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="TrendingDown" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock x:Name="ExtraRemainingLabel" Text="Extra Left:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="ExtraRemainingText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!-- Down Payment Card -->
                            <materialDesign:Card Margin="0,8,0,0" Padding="16" Background="#FFF3E0" BorderBrush="#FFB74D" BorderThickness="1">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="CashMultiple" Width="14" Height="14" Foreground="#FF8F00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Down Payment:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="DownPaymentAmountText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#FF8F00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="Percent" Width="14" Height="14" Foreground="#FF8F00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="Down Payment %:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock x:Name="DownPaymentPercentageText" Text="0%" FontSize="14" FontWeight="Bold" Foreground="#FF8F00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!-- Invoices Content (Hidden by default) -->
            <ScrollViewer x:Name="InvoicesContent" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Visibility="Collapsed" Padding="0,0,12,0" CanContentScroll="False">
                <StackPanel>
                    <!-- Invoices Header and Controls -->
                    <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" Foreground="#4A5568" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="Project Invoices" FontSize="20" FontWeight="SemiBold" Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Button Grid.Column="1" x:Name="ExportToExcelButton"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        BorderBrush="#059669" Foreground="#059669" Padding="16,10" FontWeight="Medium"
                                        Margin="0,0,12,0" Click="ExportToExcelButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="Export to Excel" FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>

                                <Button Grid.Column="2" x:Name="AddInvoiceButton"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Background="#4A5568" Padding="20,10" FontWeight="Medium" Click="AddInvoiceButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0" Foreground="White"/>
                                        <TextBlock Text="Add New Invoice" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </Grid>

                            <!-- Invoice Filters -->
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="InvoiceSearchTextBox" Grid.Column="0"
                                         materialDesign:HintAssist.Hint="Search invoices..."
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Margin="0,0,8,0" TextChanged="InvoiceSearchTextBox_TextChanged"/>

                                <ComboBox x:Name="TypeFilterCombo" Grid.Column="1"
                                          materialDesign:HintAssist.Hint="Type Filter"
                                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                          Margin="0,0,8,0" SelectionChanged="InvoiceFilters_Changed">
                                    <!-- Items will be populated dynamically based on project settings -->
                                </ComboBox>

                                <ComboBox x:Name="SiteFilterCombo" Grid.Column="2"
                                          materialDesign:HintAssist.Hint="Site Filter"
                                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                          Margin="0,0,8,0" SelectionChanged="InvoiceFilters_Changed">
                                    <ComboBoxItem Content="All Sites" IsSelected="True"/>
                                </ComboBox>

                                <Button Grid.Column="3" x:Name="ClearInvoiceFilterButton" Content="Clear"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Padding="12,8" Click="ClearInvoiceFilterButton_Click"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Invoices DataGrid -->
                    <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="0" Margin="0,0,0,16">
                        <DataGrid x:Name="InvoicesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                  IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column" RowHeight="80" ColumnWidth="*" Background="White"
                                  BorderThickness="0" ScrollViewer.CanContentScroll="False" ScrollViewer.VerticalScrollBarVisibility="Auto" ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                  PreviewMouseWheel="DataGrid_PreviewMouseWheel">
                            <DataGrid.Resources>
                                <!-- Style for paid invoices -->
                                <Style TargetType="DataGridRow">
                                    <Style.Triggers>
                                        <!-- Fully Paid - Green -->
                                        <DataTrigger Binding="{Binding IsFullyPaid}" Value="True">
                                            <Setter Property="Background" Value="#ECFDF5"/>
                                            <Setter Property="BorderBrush" Value="#10B981"/>
                                            <Setter Property="BorderThickness" Value="0,0,4,0"/>
                                        </DataTrigger>
                                        <!-- Partially Paid - Orange -->
                                        <DataTrigger Binding="{Binding IsPartiallyPaid}" Value="True">
                                            <Setter Property="Background" Value="#FFFBEB"/>
                                            <Setter Property="BorderBrush" Value="#F59E0B"/>
                                            <Setter Property="BorderThickness" Value="0,0,4,0"/>
                                        </DataTrigger>
                                        <!-- Unpaid - Red -->
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsFullyPaid}" Value="False"/>
                                                <Condition Binding="{Binding IsPartiallyPaid}" Value="False"/>
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Background" Value="#FEF2F2"/>
                                            <Setter Property="BorderBrush" Value="#EF4444"/>
                                            <Setter Property="BorderThickness" Value="0,0,4,0"/>
                                        </MultiDataTrigger>
                                        <!-- Hover effects -->
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F1F5F9"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Resources>
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Invoice No." Binding="{Binding InvoiceNumber}" Width="100"/>
                                <DataGridTextColumn Header="Type" Binding="{Binding TypeDisplay}" Width="120"/>

                                <!-- Payment Terms Details with actual payment term name -->
                                <DataGridTemplateColumn Header="Payment Terms Details" Width="400">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Margin="6,4">
                                                <TextBlock Text="{Binding PaymentTermsInfo}"
                                                         FontSize="10" Foreground="#333333"
                                                         TextWrapping="Wrap" LineHeight="14"
                                                         FontFamily="Consolas, Courier New, monospace"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTextColumn Header="Site" Binding="{Binding SitesInfo}" Width="90"/>
                                <DataGridTextColumn Header="Amount" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="100"/>
                                <DataGridTextColumn Header="Exchange Rate" Binding="{Binding ExchangeRate, StringFormat='{}{0:F2}'}" Width="110"/>
                                <DataGridTextColumn Header="Paid" Binding="{Binding PaidAmount, StringFormat='{}{0:C0}'}" Width="90"/>
                                <DataGridTextColumn Header="Remaining" Binding="{Binding RemainingAmount, StringFormat='{}{0:C0}'}" Width="100"/>



                                <!-- Dates and Status with colored indicators -->
                                <DataGridTemplateColumn Header="Dates &amp; Status" Width="180">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Margin="4">
                                                <!-- Arrival Date with Red indicator -->
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <Ellipse Width="8" Height="8" Fill="Red" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                    <TextBlock FontSize="11" Foreground="Black" FontWeight="SemiBold"
                                                               Text="{Binding ArrivalDate, StringFormat='Arrival: {0:dd/MM/yyyy}', TargetNullValue='Arrival: Not Set'}"/>
                                                </StackPanel>
                                                <!-- Signature Date with Green indicator -->
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <Ellipse Width="8" Height="8" Fill="Green" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                    <TextBlock FontSize="11" Foreground="Black" FontWeight="SemiBold"
                                                               Text="{Binding SignatureDate, StringFormat='Signed: {0:dd/MM/yyyy}', TargetNullValue='Signed: Not Signed'}"/>
                                                </StackPanel>
                                                <!-- Status -->
                                                <TextBlock Text="{Binding Status}" FontSize="10" Foreground="Black" Margin="0,2" FontWeight="SemiBold"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Commitment with Details Button -->
                                <DataGridTemplateColumn Header="Commitment" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding CommitmentNumber}" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="9"/>
                                                <Button ToolTip="View Commitment Details"
                                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                                        Width="28" Height="28" Margin="5,0,0,0"
                                                        Padding="2"
                                                        Background="#6366F1" Foreground="White"
                                                        Click="ViewCommitmentDetails_Click"
                                                        Tag="{Binding CommitmentId}"
                                                        Visibility="{Binding CommitmentId, Converter={StaticResource NullToVisibilityConverter}}">
                                                    <materialDesign:PackIcon Kind="Handshake" Width="16" Height="16"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="Description" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Description}"
                                                       TextTrimming="CharacterEllipsis"
                                                       ToolTip="{Binding Description}"
                                                       ToolTipService.ShowDuration="30000"
                                                       ToolTipService.InitialShowDelay="500"
                                                       Padding="4"
                                                       VerticalAlignment="Center"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="Actions" Width="280">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <!-- File Actions -->
                                                <Button Content="Letter" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="1,1" Padding="6,2" FontSize="8" Height="22" MinWidth="35"
                                                        Background="{Binding LetterFileName, Converter={StaticResource LetterAttachmentBackgroundConverter}}"
                                                        Foreground="{Binding LetterFileName, Converter={StaticResource LetterAttachmentForegroundConverter}}"
                                                        BorderBrush="{Binding LetterFileName, Converter={StaticResource LetterAttachmentBorderConverter}}"
                                                        ToolTip="Invoice Letter File"
                                                        Click="InvoiceLetterButton_Click"/>

                                                <Button Content="File" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="1,1" Padding="6,2" FontSize="8" Height="22" MinWidth="30"
                                                        Background="{Binding AttachedFileName, Converter={StaticResource FileAttachmentBackgroundConverter}}"
                                                        Foreground="{Binding AttachedFileName, Converter={StaticResource FileAttachmentForegroundConverter}}"
                                                        BorderBrush="{Binding AttachedFileName, Converter={StaticResource FileAttachmentBorderConverter}}"
                                                        ToolTip="Invoice File"
                                                        Click="InvoiceFilesButton_Click"/>

                                                <!-- Invoice Actions -->
                                                <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="1,1" Padding="6,2" FontSize="8" Height="22" MinWidth="30"
                                                        Background="#FFF3E0" Foreground="#F57C00" BorderBrush="#F57C00"
                                                        Click="EditInvoiceButton_Click"/>

                                                <Button Content="Copy" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="1,1" Padding="6,2" FontSize="8" Height="22" MinWidth="32"
                                                        Background="#F3E5F5" Foreground="#7B1FA2" BorderBrush="#7B1FA2"
                                                        Click="CopyInvoiceButton_Click"/>

                                                <Button Content="Del" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="1,1" Padding="6,2" FontSize="8" Height="22" MinWidth="28"
                                                        Background="#FFEBEE" Foreground="#D32F2F" BorderBrush="#D32F2F"
                                                        Click="DeleteInvoiceButton_Click"/>
                                            </WrapPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- Commitments Content (Hidden by default) -->
            <ScrollViewer x:Name="CommitmentsContent" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Visibility="Collapsed" Padding="0,0,12,0" CanContentScroll="False">
                <StackPanel>
                    <!-- Commitments Header and Controls -->
                    <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Handshake" Width="24" Height="24" Foreground="#4A5568" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="Project Commitments" FontSize="20" FontWeight="SemiBold" Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button x:Name="AddCommitmentButton"
                                            Style="{StaticResource MaterialDesignRaisedButton}"
                                            Background="#4A5568" Padding="20,10" Margin="0,0,12,0" FontWeight="Medium" Click="AddCommitmentButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0" Foreground="White"/>
                                            <TextBlock Text="Add New Commitment" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Grid>

                            <!-- Commitment Search -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="CommitmentSearchTextBox" Grid.Column="0"
                                         materialDesign:HintAssist.Hint="Search commitments..."
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Margin="0,0,8,0" TextChanged="CommitmentSearchTextBox_TextChanged"/>

                                <Button Grid.Column="1" x:Name="ClearCommitmentFilterButton" Content="Clear"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Padding="12,8" Click="ClearCommitmentFilterButton_Click"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Commitments DataGrid -->
                    <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="0" Margin="0,0,0,16">
                        <DataGrid x:Name="CommitmentsDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                  IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column" RowHeight="50" ColumnWidth="*" Background="White"
                                  BorderThickness="0" ScrollViewer.CanContentScroll="False" ScrollViewer.VerticalScrollBarVisibility="Auto" ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                  PreviewMouseWheel="DataGrid_PreviewMouseWheel">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Title" Binding="{Binding Title}" Width="2*"/>
                                <DataGridTextColumn Header="Type" Binding="{Binding TypeDisplay}" Width="*"/>
                                <DataGridTextColumn Header="Amount" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="*"/>
                                <DataGridTextColumn Header="Invoiced" Binding="{Binding TotalInvoicedAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                <!-- <DataGridTextColumn Header="Remaining (Corrected)" Binding="{Binding RemainingCommitmentAmountCorrected, StringFormat='{}{0:C0}'}" Width="*"/> -->
                                <DataGridTextColumn Header="Rate" Binding="{Binding ExchangeRate}" Width="*"/>
                                <DataGridTextColumn Header="Date" Binding="{Binding CreatedDate, StringFormat='{}{0:dd/MM/yyyy}'}" Width="*"/>
                                <DataGridTextColumn Header="Remaining (EGP)" Binding="{Binding RemainingAmountEGP, StringFormat='{}{0:N0} EGP'}" Width="*"/>
                                <DataGridTemplateColumn Header="Actions" Width="4*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2,0" Padding="6,4" FontSize="11" Height="30"
                                                        Click="EditCommitmentButton_Click"/>
                                                <Button Content="Copy" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2,0" Padding="6,4" FontSize="11" Height="30"
                                                        Click="CopyCommitmentButton_Click"/>
                                                <Button Content="Open File" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2,0" Padding="6,4" FontSize="11" Height="30"
                                                        Background="{Binding AttachedFileName, Converter={StaticResource CommitmentFileBackgroundConverter}}"
                                                        Foreground="{Binding AttachedFileName, Converter={StaticResource CommitmentFileForegroundConverter}}"
                                                        BorderBrush="{Binding AttachedFileName, Converter={StaticResource CommitmentFileBorderConverter}}"
                                                        Click="OpenCommitmentFileButton_Click"
                                                        ToolTip="Open attached commitment file"/>
                                                <Button Content="Invoices" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2,0" Padding="6,4" FontSize="11" Height="30"
                                                        Click="ViewCommitmentInvoicesButton_Click"/>
                                                <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2,0" Padding="6,4" FontSize="11" Height="30"
                                                        Click="DeleteCommitmentButton_Click"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#F7F7F7" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Padding="20,8">
            <Grid>
                <TextBlock x:Name="StatusText" Text="Ready" VerticalAlignment="Center" Foreground="#6B7280" FontSize="12"/>
                <TextBlock Text="Project Details View" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="#6B7280" FontSize="12"/>
            </Grid>
        </Border>
    </Grid>
</Window>


