#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace FinancialTracker.Models
{
    public class Invoice
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public int ProjectId { get; set; }

        public int? CommitmentId { get; set; }
        
        private decimal _amountUSD;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountUSD
        {
            get => _amountUSD;
            set => _amountUSD = value;
        }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string FixedAmountDetails { get; set; } = string.Empty;
        
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        public DateTime? SignatureDate { get; set; }

        public DateTime? ArrivalDate { get; set; }

        [Column(TypeName = "decimal(10,4)")]
        public decimal ExchangeRate { get; set; } = 1.0m;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public bool IsPaid { get; set; } = false;
        
        public DateTime? PaidDate { get; set; }

        // Partial payment support
        private decimal _paidAmount = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount
        {
            get => _paidAmount;
            set => _paidAmount = value;
        }

        [MaxLength(50)]
        public string Type { get; set; } = "Software Tasks";

        [MaxLength(500)]
        public string? AttachedFilePath { get; set; }

        [MaxLength(100)]
        public string? AttachedFileName { get; set; }

        // Letter attachment properties
        [MaxLength(500)]
        public string? LetterFilePath { get; set; }

        [MaxLength(100)]
        public string? LetterFileName { get; set; }



        // Percentage-based payment properties
        [Column(TypeName = "decimal(5,2)")]
        public decimal PaymentPercentage { get; set; } = 0; // Percentage of tasks amount (0-100)

        // Site application mode - true if applies to all sites, false if single site
        public bool AppliesAllSites { get; set; } = false;

        public bool IsPercentageBased { get; set; } = false; // Whether this invoice uses percentage calculation

        // Site-based invoice properties - New flexible approach
        public int? ProjectSiteId { get; set; } // Selected site for the invoice

        [MaxLength(100)]
        public string SiteName { get; set; } = string.Empty; // Site name for display

        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;

        [ForeignKey("CommitmentId")]
        public virtual Commitment? Commitment { get; set; }

        [ForeignKey("ProjectSiteId")]
        public virtual ProjectSite? ProjectSite { get; set; }

        public virtual ICollection<Reply> Replies { get; set; } = new List<Reply>();
        public virtual ICollection<InvoicePaymentTerm> InvoicePaymentTerms { get; set; } = new List<InvoicePaymentTerm>();

        // Computed properties
        [NotMapped]
        public decimal RemainingAmount => AmountUSD - PaidAmount;

        [NotMapped]
        public bool IsPartiallyPaid => PaidAmount > 0 && (AmountUSD - PaidAmount) > 0.01m;

        [NotMapped]
        public bool IsFullyPaid => (AmountUSD - PaidAmount) <= 0.01m && PaidAmount > 0;



        // Property to display type correctly - now shows the original type
        [NotMapped]
        public string TypeDisplay => GetTypeDisplay();

        private string GetTypeDisplay()
        {
            if (string.IsNullOrEmpty(Type))
                return "Other";

            // Return the original type as stored, no conversion
            return Type;
        }

        // Property for categorized type (for filtering and grouping)
        [NotMapped]
        public string TypeCategory => GetTypeCategory();

        private string GetTypeCategory()
        {
            if (string.IsNullOrEmpty(Type))
                return "Other";

            // If type is a number, convert it to text
            if (int.TryParse(Type, out int typeNumber))
            {
                return typeNumber switch
                {
                    1 => "Equipment",
                    2 => "Equipment",
                    3 => "SERVICES",
                    _ => "Other"
                };
            }

            // Check if type contains service-related keywords
            var lowerType = Type.ToLower();
            if (lowerType.Contains("service") || lowerType.Contains("services") ||
                lowerType.Contains("maintenance") || lowerType.Contains("support"))
            {
                return "SERVICES";
            }

            // Check if type contains spare parts keywords
            if (lowerType.Contains("spare") || lowerType.Contains("part"))
            {
                return "Spare Parts";
            }

            // Check for specific hardware types (more specific checks first)
            if (lowerType.Contains("hardware") || lowerType.Contains("hw") ||
                lowerType.Contains("dell") || lowerType.Contains("vmware") ||
                lowerType.Contains("hw sizing"))
            {
                return "Hardware";
            }

            // Check for specific software types
            if (lowerType.Contains("software") || lowerType.Contains("sw") ||
                lowerType.Contains("license") || lowerType.Contains("operating") ||
                lowerType.Contains("sw license"))
            {
                return "Software";
            }

            // Check if type contains task/programming/equipment keywords (generic equipment)
            if (lowerType.Contains("task") || lowerType.Contains("tasks") ||
                lowerType.Contains("programming") || lowerType.Contains("equipment"))
            {
                return "Equipment";
            }

            // Default mapping for common values
            return lowerType switch
            {
                "task" => "Equipment",
                "tasks" => "Equipment",
                "service" => "SERVICES",
                "services" => "SERVICES",
                "programming" => "Equipment",
                "equipment" => "Equipment",
                _ => "Equipment" // Default to Equipment if unclear
            };
        }

        [NotMapped]
        public string PaymentStatus
        {
            get
            {
                if (PaidAmount == 0) return "Unpaid";
                if (PaidAmount >= AmountUSD) return "Fully Paid";
                return "Partially Paid";
            }
        }

        [NotMapped]
        public bool IsSigned => SignatureDate.HasValue;

        [NotMapped]
        public string SignatureStatus => IsSigned ? "Signed" : "Unsigned";

        /// <summary>
        /// Whether this invoice uses multiple payment terms
        /// </summary>
        [NotMapped]
        public bool HasMultiplePaymentTerms => InvoicePaymentTerms?.Count > 1;

        /// <summary>
        /// Total calculated amount from multiple payment terms
        /// </summary>
        [NotMapped]
        public decimal TotalCalculatedAmount => InvoicePaymentTerms?.Sum(ipt => ipt.CalculatedAmount) ?? 0;

        /// <summary>
        /// Number of payment terms associated with this invoice
        /// </summary>
        [NotMapped]
        public int PaymentTermsCount => InvoicePaymentTerms?.Count ?? 0;

        /// <summary>
        /// Brief description of multiple payment terms
        /// </summary>
        [NotMapped]
        public string PaymentTermsSummary
        {
            get
            {
                if (InvoicePaymentTerms == null || !InvoicePaymentTerms.Any())
                    return IsPercentageBased ? $"{PaymentPercentage}% Payment" : "Fixed Amount";

                if (InvoicePaymentTerms.Count == 1)
                {
                    var term = InvoicePaymentTerms.First();
                    return $"{term.AppliedPercentage}% - {term.PaymentTermDescription}";
                }

                var totalPercentage = InvoicePaymentTerms.Sum(ipt => ipt.AppliedPercentage);
                return $"{InvoicePaymentTerms.Count} Terms ({totalPercentage}% total)";
            }
        }

        /// <summary>
        /// Detailed list of payment terms for display
        /// </summary>
        [NotMapped]
        public string DetailedPaymentTermsList
        {
            get
            {
                if (InvoicePaymentTerms == null || !InvoicePaymentTerms.Any())
                    return "";

                return string.Join("\n", InvoicePaymentTerms
                    .OrderBy(ipt => ipt.DisplayOrder)
                    .Select(ipt => {
                        var original = ipt.OriginalPercentage;
                        var applied = ipt.AppliedPercentage;

                        // percentage shown (per-site if needed)
                        var displayPercentage = applied;
                        bool isMultiSite = Project != null && Project.NumberOfSites > 1;
                        if (isMultiSite)
                        {
                            displayPercentage = applied / Project!.NumberOfSites;
                        }

                        // try to express it as "X% of Y% = Z%"
                        string percentText;
                        if (original > 0)
                        {
                            var x = applied * 100m / original; // X% of original
                            percentText = $"{x}% of {original}% = {displayPercentage}%" + (isMultiSite ? " per site" : "");
                        }
                        else
                        {
                            percentText = $"{displayPercentage}%" + (isMultiSite ? " per site" : "");
                        }

                        return $"• {ipt.PaymentTermDescription} ({percentText}) = ${ipt.CalculatedAmount:F2}";
                    }));
            }
        }



        [NotMapped]
        public string PaymentTypeDisplay
        {
            get
            {
                if (HasMultiplePaymentTerms)
                {
                    var totalPercentage = InvoicePaymentTerms.Sum(ipt => ipt.AppliedPercentage);
                    var termsCount = InvoicePaymentTerms.Count;

                    // If this is a multi-site project, show per-site percentage
                    if (Project != null && Project.NumberOfSites > 1)
                    {
                        var perSitePercentage = totalPercentage / Project.NumberOfSites;
                        return $"{termsCount} Terms ({perSitePercentage}% per site)";
                    }

                    return $"{termsCount} Terms ({totalPercentage}% total)";
                }

                if (IsPercentageBased)
                {
                    return $"Single Term ({PaymentPercentage}%)";
                }

                return "Fixed Amount";
            }
        }

        [NotMapped]
        public string SiteTypeDisplay
        {
            get
            {
                if (AppliesAllSites)
                {
                    return "🌐 All Sites Combined";
                }

                if (!string.IsNullOrEmpty(SiteName))
                {
                    return $"📍 {SiteName}";
                }
                return ProjectSite?.SiteName ?? "Not Specified";
            }
        }

        [NotMapped]
        public decimal CalculatedAmountFromPercentage
        {
            get
            {
                if (!IsPercentageBased || Project == null) return 0;

                // Select base pool by category (keep categories independent)
                var typeLower = (Type ?? string.Empty).ToLower();
                decimal basePool;

                if (typeLower.Contains("service"))
                {
                    basePool = Project.ServicesAmount; // Services only
                }
                else if (typeLower.Contains("spare") || typeLower.Contains("part"))
                {
                    basePool = Project.SparePartsAmount; // Spare parts only
                }
                else
                {
                    basePool = Project.DistributableTasksAmount; // Equipment/Tasks only
                }

                var baseAmount = basePool * PaymentPercentage / 100;

                // Apply site-based calculation - divide by number of sites
                // Only if AppliesAllSites is false
                if (Project.NumberOfSites > 1 && !AppliesAllSites)
                {
                    return baseAmount / Project.NumberOfSites;
                }

                // Single site project or applies to all sites: 100% of the percentage
                return baseAmount;
            }
        }



        // Method to calculate amount from percentage
        public void CalculateAmountFromPercentage(decimal tasksAmount)
        {
            if (IsPercentageBased && PaymentPercentage > 0)
            {
                var baseAmount = tasksAmount * PaymentPercentage / 100;

                // Apply site-based calculation - divide by number of sites
                // Only if AppliesAllSites is false
                if (Project?.NumberOfSites > 1 && !AppliesAllSites)
                {
                    AmountUSD = baseAmount / Project.NumberOfSites;
                }
                else
                {
                    // Single site project or applies to all sites: 100% of the percentage
                    AmountUSD = baseAmount;
                }
            }
        }

        /// <summary>
        /// حساب المبلغ الإجمالي من شروط الدفع المتعددة
        /// </summary>
        /// <param name="baseAmount">المبلغ الأساسي (Tasks أو Services)</param>
        /// <param name="numberOfSites">عدد المواقع</param>
        public void CalculateAmountFromMultiplePaymentTerms(decimal baseAmount, int numberOfSites = 1)
        {
            if (InvoicePaymentTerms == null || !InvoicePaymentTerms.Any())
                return;

            decimal totalAmount = 0;

            foreach (var paymentTerm in InvoicePaymentTerms)
            {
                paymentTerm.CalculateAmount(baseAmount, numberOfSites);
                totalAmount += paymentTerm.CalculatedAmount;
            }

            AmountUSD = totalAmount;
        }

        /// <summary>
        /// إضافة شرط دفع جديد للفاتورة
        /// </summary>
        /// <param name="projectPaymentTermId">معرف شرط الدفع</param>
        /// <param name="appliedPercentage">النسبة المطبقة</param>
        /// <param name="displayOrder">ترتيب العرض</param>
        public void AddPaymentTerm(int projectPaymentTermId, decimal appliedPercentage, int displayOrder = 1)
        {
            var invoicePaymentTerm = new InvoicePaymentTerm
            {
                InvoiceId = this.Id,
                ProjectPaymentTermId = projectPaymentTermId,
                AppliedPercentage = appliedPercentage,
                DisplayOrder = displayOrder,
                CreatedDate = DateTime.Now
            };

            InvoicePaymentTerms.Add(invoicePaymentTerm);
        }

        /// <summary>
        /// إزالة جميع شروط الدفع من الفاتورة
        /// </summary>
        public void ClearPaymentTerms()
        {
            InvoicePaymentTerms.Clear();
        }

        // ===== خصائص العرض الجديدة للجدول =====

        /// <summary>
        /// معلومات المواقع للعرض في الجدول
        /// </summary>
        [NotMapped]
        public string SitesInfo
        {
            get
            {
                if (Project == null) return "N/A";

                if (AppliesAllSites)
                {
                    return $"All ({Project.NumberOfSites})";
                }
                else if (!string.IsNullOrEmpty(SiteName))
                {
                    return SiteName;
                }
                else
                {
                    return "Single Site";
                }
            }
        }

        /// <summary>
        /// معلومات شروط الدفع للعرض في الجدول
        /// </summary>
        [NotMapped]
        public string PaymentTermsInfo
        {
            get
            {
                try
                {
                    // If has multiple payment terms, show them
                    if (HasMultiplePaymentTerms && InvoicePaymentTerms?.Any() == true)
                    {
                        var lines = new List<string>();

                        foreach (var ipt in InvoicePaymentTerms.OrderBy(ipt => ipt.DisplayOrder))
                        {
                            var termName = ipt.ProjectPaymentTerm?.Description ??
                                          ipt.PaymentTermDescription ??
                                          "Unknown Term";

                            var originalPercentage = ipt.ProjectPaymentTerm?.Percentage ?? ipt.AppliedPercentage;
                            var appliedPercentage = ipt.AppliedPercentage;
                            var amount = ipt.CalculatedAmount;

                            // Show detailed breakdown based on mode (site info is in separate column)
                            var termDisplay = $"📋 {termName}";
                            lines.Add(termDisplay);

                            // Add description if available
                            if (!string.IsNullOrEmpty(Description))
                            {
                                lines.Add($"   Description: {Description}");
                            }

                            // Check if this is full term mode (applied percentage equals original percentage)
                            if (Math.Abs(appliedPercentage - originalPercentage) < 0.01m)
                            {
                                // Full term mode
                                lines.Add($"   Full Term: {appliedPercentage:F1}% of project (no site division)");
                            }
                            else if (Project?.NumberOfSites > 1)
                            {
                                // Percentage of term mode or site-divided mode
                                var perSitePercentage = originalPercentage / Project.NumberOfSites;

                                if (Math.Abs(appliedPercentage - perSitePercentage) < 0.01m)
                                {
                                    // Standard site division
                                    lines.Add($"   Site Division: {originalPercentage:F1}% ÷ {Project.NumberOfSites} sites = {appliedPercentage:F2}% per site");
                                }
                                else
                                {
                                    // Percentage of term mode
                                    var usagePercentage = (appliedPercentage / perSitePercentage) * 100;
                                    lines.Add($"   Original: {originalPercentage:F1}% ÷ {Project.NumberOfSites} sites = {perSitePercentage:F1}% per site");
                                    lines.Add($"   Used: {usagePercentage:F0}% of {perSitePercentage:F1}% = {appliedPercentage:F2}% of project");
                                }
                            }
                            else
                            {
                                // Single site
                                lines.Add($"   Percentage: {appliedPercentage:F2}% of project");
                            }
                        }

                        // Add total
                        var totalPercentage = InvoicePaymentTerms.Sum(ipt => ipt.AppliedPercentage);
                        lines.Add($"💰 Total: {totalPercentage:F2}%");

                        return string.Join("\n", lines);
                    }
                    else
                    {
                        // Single payment term or fixed amount - show clear info
                        var lines = new List<string>();

                        // For percentage-based invoices, try to get the actual payment term used
                        if (IsPercentageBased && InvoicePaymentTerms?.Any() == true)
                        {
                            // Get the actual payment term used for this invoice
                            var firstPaymentTerm = InvoicePaymentTerms.FirstOrDefault();
                            if (firstPaymentTerm?.ProjectPaymentTerm != null)
                            {
                                var paymentTerm = firstPaymentTerm.ProjectPaymentTerm;
                                lines.Add($"📋 {paymentTerm.Description}");

                                // Show percentage details
                                if (firstPaymentTerm.AppliedPercentage != paymentTerm.Percentage)
                                {
                                    lines.Add($"   Applied: {firstPaymentTerm.AppliedPercentage:F1}% of {paymentTerm.Percentage:F1}%");
                                }
                                else
                                {
                                    lines.Add($"   Percentage: {firstPaymentTerm.AppliedPercentage:F1}%");
                                }

                                // Site information is shown in separate column, not here
                            }
                        }
                        else
                        {
                            // For fixed amount invoices or when no specific payment terms
                            if (PaymentPercentage > 0)
                            {
                                lines.Add($"📋 {TypeDisplay} Payment ({PaymentPercentage:F2}% of project)");
                            }
                            else
                            {
                                lines.Add($"📋 {TypeDisplay} - Fixed Amount Payment");
                                lines.Add($"   Amount: ${AmountUSD:N2} USD");

                                // Show custom fixed amount details if available
                                if (!string.IsNullOrEmpty(FixedAmountDetails))
                                {
                                    lines.Add($"   Details: {FixedAmountDetails}");
                                }
                            }

                            // Show description if available for fixed amount invoices
                            if (!string.IsNullOrEmpty(Description))
                            {
                                lines.Add($"   Description: {Description}");
                            }
                        }

                        return string.Join("\n", lines);
                    }
                }
                catch (Exception)
                {
                    return $"• Payment terms:\n• Amount: {AmountUSD:C0}\n• ({PaymentPercentage}%) = ${(AmountUSD * PaymentPercentage / 100):F1}K";
                }
            }
        }

        /// <summary>
        /// معلومات وضع الموقع للعرض في الجدول
        /// </summary>
        [NotMapped]
        public string SiteModeInfo
        {
            get
            {
                if (Project?.NumberOfSites <= 1) return "Single Site";

                if (AppliesAllSites)
                {
                    return "All Sites Combined";
                }
                else
                {
                    return "Per Site (Divided)";
                }
            }
        }

        /// <summary>
        /// رقم الالتزام للعرض في الجدول
        /// </summary>
        [NotMapped]
        public string CommitmentNumber
        {
            get
            {
                return Commitment?.Id.ToString() ?? "N/A";
            }
        }

        /// <summary>
        /// تاريخ الفاتورة بدون وقت للعرض
        /// </summary>
        [NotMapped]
        public string InvoiceDateDisplay
        {
            get
            {
                return InvoiceDate.ToString("dd/MM/yyyy");
            }
        }

        /// <summary>
        /// تاريخ الإنشاء بدون وقت للعرض
        /// </summary>
        [NotMapped]
        public string CreatedDateDisplay
        {
            get
            {
                return CreatedDate.ToString("dd/MM/yyyy");
            }
        }
    }
}
