@echo off
REM ========================================
REM Financial Tracker - Build Script
REM ========================================

echo.
echo ========================================
echo Financial Tracker - Build
echo ========================================
echo.

REM Step 1: Clean old build files
echo [1/5] Cleaning old build files...
if exist "bin" (
    rmdir /s /q "bin"
    echo   - Deleted bin folder
)
if exist "obj" (
    rmdir /s /q "obj"
    echo   - Deleted obj folder
)
if exist "publish" (
    rmdir /s /q "publish"
    echo   - Deleted publish folder
)
echo   Done!
echo.

REM Step 2: Restore dependencies
echo [2/5] Restoring dependencies...
dotnet restore
if errorlevel 1 (
    echo   ERROR: Failed to restore dependencies
    pause
    exit /b 1
)
echo   Done!
echo.

REM Step 3: Build in Release mode
echo [3/5] Building in Release mode...
dotnet build --configuration Release --no-restore
if errorlevel 1 (
    echo   ERROR: Build failed
    pause
    exit /b 1
)
echo   Done!
echo.

REM Step 4: Publish with compression
echo [4/5] Publishing with compression enabled...
dotnet publish FinancialTracker.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output "publish" ^
    -p:PublishSingleFile=true ^
    -p:EnableCompressionInSingleFile=true ^
    -p:PublishReadyToRun=true ^
    -p:IncludeNativeLibrariesForSelfExtract=true

if errorlevel 1 (
    echo   ERROR: Publish failed
    pause
    exit /b 1
)
echo   Done!
echo.

REM Step 5: Copy and show results
echo [5/5] Finalizing...
if exist "publish\FinancialTracker.exe" (
    copy /y "publish\FinancialTracker.exe" "FinancialTracker.exe" >nul
    echo   - Created FinancialTracker.exe

    echo.
    echo ========================================
    echo Build Complete!
    echo ========================================
    echo.
    echo Output file: FinancialTracker.exe
    echo Location: %CD%
    echo.
    echo ========================================
) else (
    echo   ERROR: Output file not found
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul

