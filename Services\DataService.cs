#nullable enable
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FinancialTracker.Data;
using FinancialTracker.Models;
using FinancialTracker.Helpers;

namespace FinancialTracker.Services
{
    public interface IDataService
    {
        Task<List<Project>> GetProjectsAsync();
        Task<Project?> GetProjectByIdAsync(int id);
        Task<Project> AddProjectAsync(Project project);
        Task<Project> UpdateProjectAsync(Project project);
        Task<bool> DeleteProjectAsync(int id);

        Task<List<Invoice>> GetInvoicesAsync();
        Task<List<Invoice>> GetInvoicesByProjectAsync(int projectId);
        Task<Invoice?> GetInvoiceByIdAsync(int id);
        Task<Invoice> AddInvoiceAsync(Invoice invoice);
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int id);
        Task<bool> RemoveInvoiceFromCommitmentAsync(int invoiceId);

        Task<List<Commitment>> GetCommitmentsAsync();
        Task<List<Commitment>> GetCommitmentsByProjectAsync(int projectId);
        Task<Commitment?> GetCommitmentByIdAsync(int id);
        Task<Commitment> AddCommitmentAsync(Commitment commitment);
        Task<Commitment> UpdateCommitmentAsync(Commitment commitment);
        Task<bool> DeleteCommitmentAsync(int id);

        Task<DashboardData> GetDashboardDataAsync();
        Task<List<ProjectSummary>> GetProjectsSummaryAsync();

        // Project Sites
        Task<List<ProjectSite>> GetProjectSitesAsync(int projectId);
        Task<ProjectSite> AddProjectSiteAsync(ProjectSite site);
        Task<ProjectSite> UpdateProjectSiteAsync(ProjectSite site);
        Task<bool> DeleteProjectSiteAsync(int siteId);
        Task<bool> DeleteProjectSitesAsync(int projectId);

        // Project Invoice Types
        Task<List<ProjectInvoiceType>> GetProjectInvoiceTypesAsync(int projectId);
        Task<ProjectInvoiceType> AddProjectInvoiceTypeAsync(ProjectInvoiceType invoiceType);
        Task<bool> DeleteProjectInvoiceTypesAsync(int projectId);

        // Project Payment Terms
        Task<List<ProjectPaymentTerm>> GetProjectPaymentTermsAsync(int projectId, string category);
        Task<List<ProjectPaymentTerm>> GetProjectPaymentTermsAsync(int projectId);
        Task<ProjectPaymentTerm> AddProjectPaymentTermAsync(ProjectPaymentTerm paymentTerm);
        Task<bool> DeleteProjectPaymentTermsAsync(int projectId);

        // Invoice Payment Terms - Multiple payment terms per invoice
        Task<List<InvoicePaymentTerm>> GetInvoicePaymentTermsAsync(int invoiceId);
        Task<InvoicePaymentTerm> AddInvoicePaymentTermAsync(InvoicePaymentTerm invoicePaymentTerm);
        Task<bool> DeleteInvoicePaymentTermsAsync(int invoiceId);
        Task<bool> UpdateInvoicePaymentTermAsync(InvoicePaymentTerm invoicePaymentTerm);
        Task<List<ProjectPaymentTerm>> GetAvailablePaymentTermsForInvoiceAsync(int projectId, string category);

        // Site Progress Methods
        Task<List<Invoice>> GetPaidInvoicesForSiteAsync(int projectId, int siteId);

        // Project Files
        Task<List<ProjectFile>> GetProjectFilesAsync(int projectId);
        Task<ProjectFile?> GetProjectFileByIdAsync(int id);
        Task<ProjectFile> AddProjectFileAsync(ProjectFile projectFile);
        Task<ProjectFile> UpdateProjectFileAsync(ProjectFile projectFile);
        Task<bool> DeleteProjectFileAsync(int id);
    }

    public class DashboardData
    {
        public int TotalProjects { get; set; }
        public int TotalInvoices { get; set; }
        public int TotalCommitments { get; set; }

        // Detailed commitment statistics
        public int TaskCommitments { get; set; }
        public int ServiceCommitments { get; set; }
        public int OtherCommitments { get; set; }

        public decimal TotalInvoiceAmount { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public decimal TotalUnpaidAmount { get; set; }
        public int PaidInvoicesCount { get; set; }
        public int UnpaidInvoicesCount { get; set; }

        // Financial statistics for commitments
        public decimal TaskCommitmentsAmount { get; set; }
        public decimal ServiceCommitmentsAmount { get; set; }
        public decimal OtherCommitmentsAmount { get; set; }
    }

    public class DataService : IDataService, IDisposable
    {
        private readonly FinancialContext _context;
        private bool _disposed = false;

        public DataService(FinancialContext context)
        {
            _context = context;
        }

        public async Task<List<Project>> GetProjectsAsync()
        {
            return await _context.Projects
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            return await _context.Projects
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .Include(p => p.Sites)
                .Include(p => p.PaymentTerms.Where(pt => pt.IsActive))
                .Include(p => p.ProjectFiles.Where(pf => pf.IsActive))
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Project> AddProjectAsync(Project project)
        {
            try
            {
                // Validate project data
                var validation = ValidateProject(project);
                if (!validation.IsValid)
                {
                    var errorMessage = $"Project validation failed: {validation.GetErrorMessage()}";
                    throw new ArgumentException(errorMessage);
                }

                // Check for duplicate project names
                var existingProject = await _context.Projects
                    .FirstOrDefaultAsync(p => p.Name.ToLower() == project.Name.ToLower());
                if (existingProject != null)
                {
                    var errorMessage = $"A project with the name '{project.Name}' already exists.";
                    throw new InvalidOperationException(errorMessage);
                }

                _context.Projects.Add(project);
                await _context.SaveChangesAsync();

                // Log activity for project creation
                await LogProjectActivityAsync("Created", project);

                return project;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<Project> UpdateProjectAsync(Project project)
        {
            _context.Projects.Update(project);
            await _context.SaveChangesAsync();

            // Log activity for project update
            await LogProjectActivityAsync("Updated", project);

            return project;
        }

        public async Task<bool> DeleteProjectAsync(int id)
        {
            try
            {
                // Use raw SQL for much faster deletion
                // This bypasses Entity Framework tracking and executes direct SQL commands

                // Get invoice and commitment IDs first
                var invoiceIds = await _context.Invoices
                    .Where(i => i.ProjectId == id)
                    .Select(i => i.Id)
                    .ToListAsync();

                var commitmentIds = await _context.Commitments
                    .Where(c => c.ProjectId == id)
                    .Select(c => c.Id)
                    .ToListAsync();

                // Execute all deletions in a single transaction using raw SQL
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // Delete invoice payment terms
                    if (invoiceIds.Any())
                    {
                        await _context.Database.ExecuteSqlRawAsync(
                            $"DELETE FROM InvoicePaymentTerms WHERE InvoiceId IN ({string.Join(",", invoiceIds)})");
                    }

                    // Delete replies for invoices
                    if (invoiceIds.Any())
                    {
                        await _context.Database.ExecuteSqlRawAsync(
                            $"DELETE FROM Replies WHERE InvoiceId IN ({string.Join(",", invoiceIds)})");
                    }

                    // Delete replies for commitments
                    if (commitmentIds.Any())
                    {
                        await _context.Database.ExecuteSqlRawAsync(
                            $"DELETE FROM Replies WHERE CommitmentId IN ({string.Join(",", commitmentIds)})");
                    }

                    // Delete invoices
                    if (invoiceIds.Any())
                    {
                        await _context.Database.ExecuteSqlRawAsync(
                            $"DELETE FROM Invoices WHERE ProjectId = {id}");
                    }

                    // Delete commitments
                    if (commitmentIds.Any())
                    {
                        await _context.Database.ExecuteSqlRawAsync(
                            $"DELETE FROM Commitments WHERE ProjectId = {id}");
                    }

                    // Delete payment terms
                    await _context.Database.ExecuteSqlRawAsync(
                        $"DELETE FROM ProjectPaymentTerms WHERE ProjectId = {id}");

                    // Delete invoice types
                    await _context.Database.ExecuteSqlRawAsync(
                        $"DELETE FROM ProjectInvoiceTypes WHERE ProjectId = {id}");

                    // Delete sites
                    await _context.Database.ExecuteSqlRawAsync(
                        $"DELETE FROM ProjectSites WHERE ProjectId = {id}");

                    // Soft delete project files
                    await _context.Database.ExecuteSqlRawAsync(
                        $"UPDATE ProjectFiles SET IsActive = 0 WHERE ProjectId = {id}");

                    // Delete activities related to this project
                    await _context.Database.ExecuteSqlRawAsync(
                        $"DELETE FROM Activities WHERE ProjectId = {id}");

                    // Finally delete the project itself
                    await _context.Database.ExecuteSqlRawAsync(
                        $"DELETE FROM Projects WHERE Id = {id}");

                    await transaction.CommitAsync();

                    System.Diagnostics.Debug.WriteLine($"Successfully deleted project {id} using raw SQL");
                    return true;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                // Log detailed error for debugging
                System.Diagnostics.Debug.WriteLine($"Error deleting project {id}: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                // Provide user-friendly error message
                throw new Exception($"Failed to delete project. Error: {ex.Message}\n\nPlease try again or contact technical support.", ex);
            }
        }

        public async Task<List<Invoice>> GetInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Project)
                    .ThenInclude(p => p.PaymentTerms)
                .Include(i => i.Commitment)
                .Include(i => i.InvoicePaymentTerms)
                    .ThenInclude(ipt => ipt.ProjectPaymentTerm)
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<Invoice>> GetInvoicesByProjectAsync(int projectId)
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.InvoicePaymentTerms)
                    .ThenInclude(ipt => ipt.ProjectPaymentTerm)
                .Where(i => i.ProjectId == projectId)
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int id)
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.InvoicePaymentTerms)
                    .ThenInclude(ipt => ipt.ProjectPaymentTerm)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<Invoice> AddInvoiceAsync(Invoice invoice)
        {
            // If invoice is linked to a commitment, validate only (do not sync type)
            if (invoice.CommitmentId.HasValue)
            {
                var commitment = await _context.Commitments
                    .Include(c => c.Invoices)
                    .FirstOrDefaultAsync(c => c.Id == invoice.CommitmentId.Value);

                if (commitment != null)
                {
                    // Validate commitment has enough remaining amount (using corrected calculation)
                    var remainingAmountCorrected = commitment.RemainingCommitmentAmountCorrected;

                    // Convert invoice amount to EGP using invoice exchange rate, then back to USD using commitment rate
                    var invoiceAmountEGP = invoice.AmountUSD * invoice.ExchangeRate;
                    var invoiceAmountInCommitmentTerms = invoiceAmountEGP / commitment.ExchangeRate;

                    if (remainingAmountCorrected < invoiceAmountInCommitmentTerms)
                    {
                        throw new InvalidOperationException(
                            $"Cannot add invoice. Commitment remaining amount (${remainingAmountCorrected:N2}) " +
                            $"is less than invoice amount in commitment terms (${invoiceAmountInCommitmentTerms:N2}). " +
                            $"Invoice: ${invoice.AmountUSD:N2} @ {invoice.ExchangeRate:F2} = EGP {invoiceAmountEGP:N0}. " +
                            $"Commitment rate: {commitment.ExchangeRate:F2}");
                    }

                    // Invoice added to commitment
                }
                else
                {
                    throw new InvalidOperationException($"Commitment with ID {invoice.CommitmentId.Value} not found.");
                }
            }

            _context.Invoices.Add(invoice);
            await _context.SaveChangesAsync();

            // Log activity for invoice creation
            await LogInvoiceActivityAsync("Created", invoice);

            return invoice;
        }

        public async Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
        {

            var existingInvoice = await _context.Invoices
                .Include(i => i.Commitment)
                .FirstOrDefaultAsync(i => i.Id == invoice.Id);

            if (existingInvoice == null)
            {
                throw new ArgumentException("Invoice not found");
            }

            // Check if commitment is changing
            var oldCommitmentId = existingInvoice.CommitmentId;
            var newCommitmentId = invoice.CommitmentId;

            // If linking to a new commitment, validate it (do not sync type)
            if (newCommitmentId.HasValue && newCommitmentId != oldCommitmentId)
            {
                var newCommitment = await _context.Commitments
                    .Include(c => c.Invoices)
                    .FirstOrDefaultAsync(c => c.Id == newCommitmentId.Value);

                if (newCommitment != null)
                {
                    // Calculate remaining amount in new commitment (using corrected calculation)
                    var remainingAmountCorrected = newCommitment.RemainingCommitmentAmountCorrected;

                    // Convert invoice amount to commitment terms
                    var invoiceAmountEGP = invoice.AmountUSD * invoice.ExchangeRate;
                    var invoiceAmountInCommitmentTerms = invoiceAmountEGP / newCommitment.ExchangeRate;

                    if (remainingAmountCorrected < invoiceAmountInCommitmentTerms)
                    {
                        throw new InvalidOperationException(
                            $"Cannot link invoice to commitment. Remaining amount (${remainingAmountCorrected:N2}) " +
                            $"is less than invoice amount in commitment terms (${invoiceAmountInCommitmentTerms:N2}).");
                    }
                }
            }
            // When staying with the same commitment, do nothing regarding invoice type

            // Update all properties
            _context.Entry(existingInvoice).CurrentValues.SetValues(invoice);
            await _context.SaveChangesAsync();

            // Log activity for invoice update
            await LogInvoiceActivityAsync("Updated", existingInvoice);

            // Commitment changes handled silently

            return existingInvoice;
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            using var context = new FinancialContext();
            var invoice = await context.Invoices
                .Include(i => i.Project)
                .Include(i => i.InvoicePaymentTerms)
                .FirstOrDefaultAsync(i => i.Id == id);

            if (invoice == null) return false;

            // Log activity before deletion
            await LogInvoiceActivityAsync("Deleted", invoice);

            // Delete related invoice payment terms first
            if (invoice.InvoicePaymentTerms?.Any() == true)
            {
                context.InvoicePaymentTerms.RemoveRange(invoice.InvoicePaymentTerms);
            }

            context.Invoices.Remove(invoice);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveInvoiceFromCommitmentAsync(int invoiceId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null) return false;

            invoice.CommitmentId = null;
            _context.Invoices.Update(invoice);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Commitment>> GetCommitmentsAsync()
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .Include(c => c.Invoices)
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<Commitment>> GetCommitmentsByProjectAsync(int projectId)
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .Include(c => c.Invoices)
                .Where(c => c.ProjectId == projectId)
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        public async Task<Commitment?> GetCommitmentByIdAsync(int id)
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .Include(c => c.Invoices)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<Commitment> AddCommitmentAsync(Commitment commitment)
        {
            _context.Commitments.Add(commitment);
            await _context.SaveChangesAsync();

            // Log activity for commitment creation
            await LogCommitmentActivityAsync("Created", commitment);

            return commitment;
        }

        public async Task<Commitment> UpdateCommitmentAsync(Commitment commitment)
        {
            // Save the old type for comparison
            var existingCommitment = await _context.Commitments.AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == commitment.Id);

            _context.Commitments.Update(commitment);
            await _context.SaveChangesAsync();

            // Log activity for commitment update
            await LogCommitmentActivityAsync("Updated", commitment);

            // Keep commitments independent: do not propagate type changes to linked invoices

            return commitment;
        }

        // Removed: syncing invoice types from commitment to keep commitments separate

        public async Task<bool> DeleteCommitmentAsync(int id)
        {
            var commitment = await _context.Commitments.FindAsync(id);
            if (commitment == null) return false;

            // Log activity before deletion
            await LogCommitmentActivityAsync("Deleted", commitment);

            _context.Commitments.Remove(commitment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<DashboardData> GetDashboardDataAsync()
        {
            var invoices = await _context.Invoices.ToListAsync();
            var projects = await _context.Projects.ToListAsync();
            var commitments = await _context.Commitments.ToListAsync();

            // Calculate total amounts using manual values where available
            var totalTaskAmount = 0m;
            var totalServiceAmount = 0m;
            var totalOtherAmount = 0m;

            foreach (var project in projects)
            {
                var projectCommitments = commitments.Where(c => c.ProjectId == project.Id).ToList();

                var taskCommitments = projectCommitments.Where(c =>
                    c.Type == "tasks" ||
                    c.Type.Contains("task") ||
                    c.Type.Contains("tasks") ||
                    c.Type.ToLower().Contains("task") ||
                    c.Type.ToLower().Contains("software") ||
                    c.Type.ToLower().Contains("hardware") ||
                    c.Type.ToLower().Contains("equipment")).ToList();

                var serviceCommitments = projectCommitments.Where(c =>
                    c.Type == "services" ||
                    c.Type.Contains("service") ||
                    c.Type.ToLower().Contains("service")).ToList();

                var otherCommitments = projectCommitments.Where(c =>
                    !taskCommitments.Contains(c) &&
                    !serviceCommitments.Contains(c)).ToList();

                // Calculate task amount based on project configuration
                decimal projectTaskAmount = 0;
                if (project.SplitTasksIntoHardwareAndSoftware)
                {
                    // If split mode: use manual amounts if available, otherwise calculate from commitments
                    if (project.ManualSoftwareTasksAmount > 0 || project.ManualHardwareTasksAmount > 0)
                    {
                        projectTaskAmount = project.ManualSoftwareTasksAmount + project.ManualHardwareTasksAmount;
                    }
                    else
                    {
                        projectTaskAmount = 0; // Commitments are just for tracking
                    }
                }
                else
                {
                    // If combined mode: use manual amount only - commitments are just for tracking
                    projectTaskAmount = project.ManualTasksAmount;
                }

                totalTaskAmount += projectTaskAmount;
                totalServiceAmount += project.ManualServicesAmount; // Commitments are just for tracking
                totalOtherAmount += 0; // Commitments are just for tracking
            }

            // Count commitments by type across all projects
            var allTaskCommitments = commitments.Where(c =>
                c.Type == "tasks" ||
                c.Type.Contains("task") ||
                c.Type.Contains("tasks") ||
                c.Type.ToLower().Contains("task") ||
                c.Type.ToLower().Contains("software") ||
                c.Type.ToLower().Contains("hardware")).ToList();

            var allServiceCommitments = commitments.Where(c =>
                c.Type == "services" ||
                c.Type.Contains("service") ||
                c.Type.ToLower().Contains("service")).ToList();

            var allOtherCommitments = commitments.Where(c =>
                !allTaskCommitments.Contains(c) &&
                !allServiceCommitments.Contains(c)).ToList();

            return new DashboardData
            {
                TotalProjects = projects.Count,
                TotalInvoices = invoices.Count,
                TotalCommitments = commitments.Count,

                // Detailed commitment statistics
                TaskCommitments = allTaskCommitments.Count,
                ServiceCommitments = allServiceCommitments.Count,
                OtherCommitments = allOtherCommitments.Count,

                TotalInvoiceAmount = invoices.Sum(i => i.AmountUSD),
                TotalPaidAmount = invoices.Sum(i => i.PaidAmount),
                TotalUnpaidAmount = invoices.Sum(i => i.RemainingAmount),
                PaidInvoicesCount = invoices.Count(i => i.IsFullyPaid),
                UnpaidInvoicesCount = invoices.Count(i => !i.IsFullyPaid),

                // Financial statistics using manual amounts where available
                TaskCommitmentsAmount = totalTaskAmount,
                ServiceCommitmentsAmount = totalServiceAmount,
                OtherCommitmentsAmount = totalOtherAmount
            };
        }

        public async Task<List<ProjectSummary>> GetProjectsSummaryAsync()
        {
            Console.WriteLine("=== GetProjectsSummaryAsync Called ===");
            var projects = await _context.Projects.ToListAsync();
            var invoices = await _context.Invoices.ToListAsync();
            var commitments = await _context.Commitments.ToListAsync();

            Console.WriteLine($"Total Projects: {projects.Count}");
            Console.WriteLine($"Total Invoices: {invoices.Count}");
            Console.WriteLine($"Total Commitments: {commitments.Count}");

            foreach (var project in projects)
            {
                Console.WriteLine($"Project: {project.Name} - POAmount: {project.POAmount}");
            }

            var projectsSummary = projects.Select(project =>
            {
                var projectInvoices = invoices.Where(i => i.ProjectId == project.Id).ToList();
                var projectCommitments = commitments.Where(c => c.ProjectId == project.Id).ToList();

                // Calculate task and service amounts - use manual values if available, otherwise calculate from commitments
                var taskCommitments = projectCommitments.Where(c =>
                    c.Type == "tasks" ||
                    c.Type.Contains("task") ||
                    c.Type.Contains("tasks") ||
                    c.Type.ToLower().Contains("task") ||
                    c.Type.ToLower().Contains("software") ||
                    c.Type.ToLower().Contains("hardware")).ToList();

                var serviceCommitments = projectCommitments.Where(c =>
                    c.Type == "services" ||
                    c.Type.Contains("service") ||
                    c.Type.ToLower().Contains("service")).ToList();

                var otherCommitments = projectCommitments.Where(c =>
                    !taskCommitments.Contains(c) &&
                    !serviceCommitments.Contains(c)).ToList();

                // Calculate task amount based on project configuration
                decimal taskAmount = 0;
                if (project.SplitTasksIntoHardwareAndSoftware)
                {
                    // If split mode: use manual amounts if available, otherwise calculate from commitments
                    if (project.ManualSoftwareTasksAmount > 0 || project.ManualHardwareTasksAmount > 0)
                    {
                        taskAmount = project.ManualSoftwareTasksAmount + project.ManualHardwareTasksAmount;
                    }
                    else
                    {
                        // Use POAmount as fallback since commitments are just for tracking
                        taskAmount = 0; // Will be calculated from POAmount later
                    }
                }
                else
                {
                    // If combined mode: use manual amount only - commitments are just for tracking
                    taskAmount = project.ManualTasksAmount;
                }

                var serviceAmount = project.ManualServicesAmount; // Commitments are just for tracking

                // Use POAmount instead of commitments for total cost
                var totalCost = project.POAmount;
                var totalPaid = projectInvoices.Sum(i => i.PaidAmount);

                // Calculate spent amounts by category - classify by invoice type only (ignore commitments)
                var taskInvoices = projectInvoices.Where(i =>
                {
                    // Classify by invoice type only - commitments are just for tracking, not for classification
                    var invoiceType = i.Type ?? "";
                    return invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase) ||
                           invoiceType.ToLower().Contains("task") || invoiceType.ToLower().Contains("software") ||
                           invoiceType.ToLower().Contains("hardware") || invoiceType.ToLower().Contains("equipment") ||
                           invoiceType.ToLower().Contains("sw") || invoiceType.ToLower().Contains("hw") ||
                           invoiceType == "1" || invoiceType == "2";
                }).ToList();

                var serviceInvoices = projectInvoices.Where(i =>
                {
                    // Classify by invoice type only - commitments are just for tracking, not for classification
                    var invoiceType = i.Type?.ToLower() ?? "";
                    return invoiceType.Contains("service") || invoiceType.Contains("خدمات") ||
                           invoiceType.Contains("maintenance") || invoiceType.Contains("support") ||
                           invoiceType == "3";
                }).ToList();

                var tasksSpent = taskInvoices.Sum(i => i.PaidAmount);
                var servicesSpent = serviceInvoices.Sum(i => i.PaidAmount);



                // Calculate file information
                var filesCount = projectInvoices.Count(i => !string.IsNullOrEmpty(i.AttachedFileName)) +
                               projectCommitments.Count(c => !string.IsNullOrEmpty(c.AttachedFileName));

                return new ProjectSummary
                {
                    Id = project.Id,
                    Name = project.Name,
                    Description = project.Description,
                    Status = project.Status,
                    CreatedDate = project.CreatedDate,
                    TotalCost = totalCost,
                    TotalPaid = totalPaid,
                    TotalInvoices = projectInvoices.Count,
                    TotalCommitments = projectCommitments.Count,
                    PaidInvoices = projectInvoices.Count(i => i.IsFullyPaid),
                    UnpaidInvoices = projectInvoices.Count(i => !i.IsFullyPaid),

                    // Detailed commitment statistics - use manual amounts if available
                    TaskCommitments = taskCommitments.Count,
                    ServiceCommitments = serviceCommitments.Count,
                    OtherCommitments = otherCommitments.Count,
                    TaskCommitmentsAmount = taskAmount,
                    ServiceCommitmentsAmount = serviceAmount,
                    OtherCommitmentsAmount = 0, // Commitments are just for tracking

                    // File information
                    FilesCount = filesCount,
                    TotalFileSize = 0, // Will be calculated later if needed

                    // PO Financial Information
                    POAmount = project.POAmount,
                    TotalSpent = totalPaid,

                    // Tasks and Services spent amounts
                    TasksSpent = tasksSpent,
                    ServicesSpent = servicesSpent,

                    // Calculated budget amounts (including spare parts)
                    TasksAmount = project.TasksAmount,
                    ServicesAmount = project.ServicesAmount
                };
            }).ToList();

            return projectsSummary;
        }

        // Project Sites Implementation
        public async Task<List<ProjectSite>> GetProjectSitesAsync(int projectId)
        {
            using var context = new FinancialContext();
            return await context.ProjectSites
                .Where(ps => ps.ProjectId == projectId && ps.IsActive)
                .OrderBy(ps => ps.SiteOrder)
                .ToListAsync();
        }

        public async Task<ProjectSite> AddProjectSiteAsync(ProjectSite site)
        {
            using var context = new FinancialContext();
            context.ProjectSites.Add(site);
            await context.SaveChangesAsync();
            return site;
        }

        public async Task<ProjectSite> UpdateProjectSiteAsync(ProjectSite site)
        {
            using var context = new FinancialContext();
            context.ProjectSites.Update(site);
            await context.SaveChangesAsync();
            return site;
        }

        public async Task<bool> DeleteProjectSiteAsync(int siteId)
        {
            using var context = new FinancialContext();
            var site = await context.ProjectSites.FindAsync(siteId);
            if (site == null) return false;

            // Check if there are invoices linked to this site
            var linkedInvoices = await context.Invoices
                .Where(i => i.ProjectSiteId == siteId)
                .ToListAsync();

            if (linkedInvoices.Any())
            {
                // Update invoices to remove site reference
                foreach (var invoice in linkedInvoices)
                {
                    invoice.ProjectSiteId = null;
                    invoice.SiteName = "Unassigned Site";
                }
                await context.SaveChangesAsync();

                // Updated invoices when deleting site
            }

            context.ProjectSites.Remove(site);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteProjectSitesAsync(int projectId)
        {
            using var context = new FinancialContext();
            var sites = await context.ProjectSites
                .Where(ps => ps.ProjectId == projectId)
                .ToListAsync();

            context.ProjectSites.RemoveRange(sites);
            await context.SaveChangesAsync();
            return true;
        }

        // Project Invoice Types Implementation
        public async Task<List<ProjectInvoiceType>> GetProjectInvoiceTypesAsync(int projectId)
        {
            using var context = new FinancialContext();
            return await context.ProjectInvoiceTypes
                .Where(pit => pit.ProjectId == projectId && pit.IsActive)
                .OrderBy(pit => pit.TypeOrder)
                .ToListAsync();
        }

        public async Task<ProjectInvoiceType> AddProjectInvoiceTypeAsync(ProjectInvoiceType invoiceType)
        {
            using var context = new FinancialContext();
            context.ProjectInvoiceTypes.Add(invoiceType);
            await context.SaveChangesAsync();
            return invoiceType;
        }

        public async Task<bool> DeleteProjectInvoiceTypesAsync(int projectId)
        {
            using var context = new FinancialContext();
            var types = await context.ProjectInvoiceTypes
                .Where(pit => pit.ProjectId == projectId)
                .ToListAsync();

            context.ProjectInvoiceTypes.RemoveRange(types);
            await context.SaveChangesAsync();
            return true;
        }

        // Project Payment Terms Implementation
        public async Task<List<ProjectPaymentTerm>> GetProjectPaymentTermsAsync(int projectId, string category)
        {
            using var context = new FinancialContext();
            return await context.ProjectPaymentTerms
                .Where(ppt => ppt.ProjectId == projectId && ppt.Category == category && ppt.IsActive)
                .OrderBy(ppt => ppt.TermOrder)
                .ToListAsync();
        }

        public async Task<ProjectPaymentTerm> AddProjectPaymentTermAsync(ProjectPaymentTerm paymentTerm)
        {
            using var context = new FinancialContext();
            context.ProjectPaymentTerms.Add(paymentTerm);
            await context.SaveChangesAsync();
            return paymentTerm;
        }

        public async Task<bool> DeleteProjectPaymentTermsAsync(int projectId)
        {
            try
            {
                using var context = new FinancialContext();

                // First, get all ProjectPaymentTerms for this project
                var terms = await context.ProjectPaymentTerms
                    .Where(ppt => ppt.ProjectId == projectId)
                    .ToListAsync();

                if (!terms.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"No payment terms found for project {projectId} - nothing to delete");
                    return true; // Nothing to delete - this is OK
                }

                // Get all ProjectPaymentTerm IDs
                var termIds = terms.Select(t => t.Id).ToList();

                // Delete all InvoicePaymentTerms that reference these ProjectPaymentTerms
                // This is necessary because of the FOREIGN KEY constraint with DeleteBehavior.Restrict
                var invoicePaymentTerms = await context.InvoicePaymentTerms
                    .Where(ipt => termIds.Contains(ipt.ProjectPaymentTermId))
                    .ToListAsync();

                if (invoicePaymentTerms.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"Deleting {invoicePaymentTerms.Count} InvoicePaymentTerms before deleting ProjectPaymentTerms");
                    context.InvoicePaymentTerms.RemoveRange(invoicePaymentTerms);
                }

                // Now delete the ProjectPaymentTerms
                System.Diagnostics.Debug.WriteLine($"Deleting {terms.Count} ProjectPaymentTerms for project {projectId}");
                context.ProjectPaymentTerms.RemoveRange(terms);

                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DeleteProjectPaymentTermsAsync: {ex.Message}");
                // Don't throw - just log and return false
                // This prevents the "expected to affect 1 row(s)" error from breaking the save operation
                return false;
            }
        }

        // Invoice Payment Terms Implementation
        public async Task<List<InvoicePaymentTerm>> GetInvoicePaymentTermsAsync(int invoiceId)
        {
            using var context = new FinancialContext();
            return await context.InvoicePaymentTerms
                .Include(ipt => ipt.ProjectPaymentTerm)
                .Where(ipt => ipt.InvoiceId == invoiceId && ipt.IsActive)
                .OrderBy(ipt => ipt.DisplayOrder)
                .ToListAsync();
        }

        public async Task<InvoicePaymentTerm> AddInvoicePaymentTermAsync(InvoicePaymentTerm invoicePaymentTerm)
        {
            using var context = new FinancialContext();
            context.InvoicePaymentTerms.Add(invoicePaymentTerm);
            await context.SaveChangesAsync();
            return invoicePaymentTerm;
        }

        public async Task<bool> DeleteInvoicePaymentTermsAsync(int invoiceId)
        {
            using var context = new FinancialContext();
            var terms = await context.InvoicePaymentTerms
                .Where(ipt => ipt.InvoiceId == invoiceId)
                .ToListAsync();

            context.InvoicePaymentTerms.RemoveRange(terms);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateInvoicePaymentTermAsync(InvoicePaymentTerm invoicePaymentTerm)
        {
            using var context = new FinancialContext();
            context.InvoicePaymentTerms.Update(invoicePaymentTerm);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<List<ProjectPaymentTerm>> GetAvailablePaymentTermsForInvoiceAsync(int projectId, string category)
        {
            using var context = new FinancialContext();
            return await context.ProjectPaymentTerms
                .Where(ppt => ppt.ProjectId == projectId && ppt.Category == category && ppt.IsActive)
                .OrderBy(ppt => ppt.TermOrder)
                .ToListAsync();
        }

        #region Validation Methods

        private ValidationResult ValidateProject(Project project)
        {
            var results = new List<ValidationResult>
            {
                ValidationHelper.ValidateRequired(project.Name, "Project Name"),
                ValidationHelper.ValidateLength(project.Name, "Project Name", 1, 200),
                ValidationHelper.ValidateSafeString(project.Name, "Project Name"),
                ValidationHelper.ValidateLength(project.Description, "Description", 0, 1000),
                ValidationHelper.ValidateSafeString(project.Description, "Description"),
                ValidationHelper.ValidateDecimal(project.POAmount.ToString(), "PO Amount", 0, decimal.MaxValue),
                ValidationHelper.ValidateInteger(project.NumberOfSites.ToString(), "Number of Sites", 1, 10, true)
            };

            return ValidationHelper.ValidateAll(results.ToArray());
        }

        private ValidationResult ValidateInvoice(Invoice invoice)
        {
            var results = new List<ValidationResult>
            {
                ValidationHelper.ValidateRequired(invoice.InvoiceNumber, "Invoice Number"),
                ValidationHelper.ValidateLength(invoice.InvoiceNumber, "Invoice Number", 1, 50),
                ValidationHelper.ValidateSafeString(invoice.InvoiceNumber, "Invoice Number"),
                ValidationHelper.ValidateDecimal(invoice.AmountUSD.ToString(), "Amount USD", 0, decimal.MaxValue, true),
                ValidationHelper.ValidateExchangeRate(invoice.ExchangeRate),
                ValidationHelper.ValidateDecimal(invoice.PaidAmount.ToString(), "Paid Amount", 0, invoice.AmountUSD),
                ValidationHelper.ValidateDate(invoice.InvoiceDate, "Invoice Date", DateTime.Now.AddYears(-10), DateTime.Now.AddYears(1), true)
            };

            if (!string.IsNullOrEmpty(invoice.Type))
            {
                results.Add(ValidationHelper.ValidateSafeString(invoice.Type, "Invoice Type"));
            }

            return ValidationHelper.ValidateAll(results.ToArray());
        }

        private ValidationResult ValidateCommitment(Commitment commitment)
        {
            var results = new List<ValidationResult>
            {
                ValidationHelper.ValidateRequired(commitment.Title, "Title"),
                ValidationHelper.ValidateLength(commitment.Title, "Title", 1, 200),
                ValidationHelper.ValidateSafeString(commitment.Title, "Title"),
                ValidationHelper.ValidateLength(commitment.Description, "Description", 0, 500),
                ValidationHelper.ValidateSafeString(commitment.Description, "Description"),
                ValidationHelper.ValidateDecimal(commitment.AmountUSD.ToString(), "Amount USD", 0, decimal.MaxValue, true),
                ValidationHelper.ValidateExchangeRate(commitment.ExchangeRate),
                ValidationHelper.ValidateDate(commitment.CreatedDate, "Created Date", DateTime.Now.AddYears(-10), DateTime.Now, true)
            };

            if (commitment.StartDate.HasValue && commitment.EndDate.HasValue)
            {
                if (commitment.StartDate > commitment.EndDate)
                {
                    results.Add(ValidationResult.Error("Start Date cannot be later than End Date"));
                }
            }

            return ValidationHelper.ValidateAll(results.ToArray());
        }

        #endregion

        #region Site Progress Methods

        public async Task<List<Invoice>> GetPaidInvoicesForSiteAsync(int projectId, int siteId)
        {
            try
            {
                using var context = new FinancialContext();
                return await context.Invoices
                    .Where(i => i.ProjectId == projectId &&
                               i.ProjectSiteId == siteId &&
                               i.PaidAmount > 0)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<Invoice>();
            }
        }

        public async Task<List<ProjectPaymentTerm>> GetProjectPaymentTermsAsync(int projectId)
        {
            try
            {
                using var context = new FinancialContext();
                return await context.ProjectPaymentTerms
                    .Where(pt => pt.ProjectId == projectId && pt.IsActive)
                    .OrderBy(pt => pt.Category)
                    .ThenBy(pt => pt.TermOrder)
                    .ToListAsync();
            }
            catch (Exception)
            {
                // Error getting project payment terms
                return new List<ProjectPaymentTerm>();
            }
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _context?.Dispose();
                }
                _disposed = true;
            }
        }

        #endregion

        #region Activity Logging

        private async Task LogActivityAsync(Activity activity)
        {
            try
            {
                // Use a separate context for activity logging to avoid concurrency issues
                using var activityContext = new FinancialContext();

                // Get next sequence number
                var lastSequence = await activityContext.Activities
                    .OrderByDescending(a => a.SequenceNumber)
                    .Select(a => a.SequenceNumber)
                    .FirstOrDefaultAsync();

                activity.SequenceNumber = lastSequence + 1;

                activityContext.Activities.Add(activity);
                await activityContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error logging activity: {ex.Message}");
                // Don't throw - activity logging should not break main operations
            }
        }

        private async Task LogProjectActivityAsync(string type, Project project, string description = "", string location = "Dashboard", string reason = "")
        {
            var activity = ActivityHelper.CreateProjectActivity(type, project, description, location, reason);
            await LogActivityAsync(activity);
        }

        private async Task LogInvoiceActivityAsync(string type, Invoice invoice, string description = "", string location = "Invoice Dialog", string reason = "")
        {
            var activity = ActivityHelper.CreateInvoiceActivity(type, invoice, description, location, reason);
            await LogActivityAsync(activity);
        }

        private async Task LogCommitmentActivityAsync(string type, Commitment commitment, string description = "", string location = "Commitment Dialog", string reason = "")
        {
            var activity = ActivityHelper.CreateCommitmentActivity(type, commitment, description, location, reason);
            await LogActivityAsync(activity);
        }

        #region Project Files Management

        public async Task<List<ProjectFile>> GetProjectFilesAsync(int projectId)
        {
            using var context = new FinancialContext();
            return await context.ProjectFiles
                .Where(pf => pf.ProjectId == projectId && pf.IsActive)
                .OrderByDescending(pf => pf.CreatedDate)
                .ToListAsync();
        }

        public async Task<ProjectFile?> GetProjectFileByIdAsync(int id)
        {
            using var context = new FinancialContext();
            return await context.ProjectFiles
                .Include(pf => pf.Project)
                .FirstOrDefaultAsync(pf => pf.Id == id);
        }

        public async Task<ProjectFile> AddProjectFileAsync(ProjectFile projectFile)
        {
            using var context = new FinancialContext();
            context.ProjectFiles.Add(projectFile);
            await context.SaveChangesAsync();

            // Log activity
            await LogProjectFileActivityAsync("CREATE", projectFile, "Project file uploaded", "Project Files Window");

            return projectFile;
        }

        public async Task<ProjectFile> UpdateProjectFileAsync(ProjectFile projectFile)
        {
            using var context = new FinancialContext();
            context.ProjectFiles.Update(projectFile);
            await context.SaveChangesAsync();

            // Log activity
            await LogProjectFileActivityAsync("UPDATE", projectFile, "Project file updated", "Project Files Window");

            return projectFile;
        }

        public async Task<bool> DeleteProjectFileAsync(int id)
        {
            using var context = new FinancialContext();
            var projectFile = await context.ProjectFiles.FindAsync(id);
            if (projectFile == null) return false;

            // Soft delete
            projectFile.IsActive = false;
            await context.SaveChangesAsync();

            // Log activity
            await LogProjectFileActivityAsync("DELETE", projectFile, "Project file deleted", "Project Files Window");

            return true;
        }

        private async Task LogProjectFileActivityAsync(string type, ProjectFile projectFile, string description = "", string location = "Project Files Window", string reason = "")
        {
            var (icon, color, title) = type switch
            {
                "CREATE" => ("Plus", "#4CAF50", $"File uploaded: {projectFile.FileName}"),
                "UPDATE" => ("Pencil", "#FF9800", $"File updated: {projectFile.FileName}"),
                "DELETE" => ("Delete", "#F44336", $"File deleted: {projectFile.FileName}"),
                _ => ("FileDocument", "#2196F3", $"File action: {projectFile.FileName}")
            };

            var activity = new Activity
            {
                Type = "ProjectFile",
                Action = type,
                Title = title,
                Description = string.IsNullOrEmpty(description) ? $"Project file: {projectFile.FileName}" : description,
                Details = $"File: {projectFile.FileName}, Size: {projectFile.FileSizeFormatted}, Category: {projectFile.Category}",
                Location = location,
                Reason = string.IsNullOrEmpty(reason) ? GetDefaultFileReason(type) : reason,
                Icon = icon,
                IconColor = color,
                ProjectId = projectFile.ProjectId,
                Timestamp = DateTime.Now
            };
            await LogActivityAsync(activity);
        }

        private static string GetDefaultFileReason(string actionType)
        {
            return actionType switch
            {
                "CREATE" => "Project file uploaded for documentation",
                "UPDATE" => "Project file information updated",
                "DELETE" => "Project file removed from system",
                _ => "Project file operation performed"
            };
        }

        #endregion

        #endregion
    }
}
