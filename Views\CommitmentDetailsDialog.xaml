<Window x:Class="FinancialTracker.Views.CommitmentDetailsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:FinancialTracker.Converters"
        Title="Commitment Details" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        Style="{StaticResource MaterialDesignWindow}"
        Background="#FAFAFA">

    <Window.Resources>
        <converters:CommitmentFileBackgroundConverter x:Key="CommitmentFileBackgroundConverter"/>
        <converters:CommitmentFileForegroundConverter x:Key="CommitmentFileForegroundConverter"/>
        <converters:CommitmentFileBorderConverter x:Key="CommitmentFileBorderConverter"/>
        <converters:PaymentStatusBackgroundConverter x:Key="PaymentStatusBackgroundConverter"/>
        <converters:PaymentStatusForegroundConverter x:Key="PaymentStatusForegroundConverter"/>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- Header Text Style -->
        <Style x:Key="HeaderText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1565C0"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- Subtitle Text Style -->
        <Style x:Key="SubtitleText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- Main Content with Modern Layout -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="24">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Modern Header Section -->
                <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCard}" Margin="0,0,0,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="{Binding Title}" Style="{StaticResource HeaderText}"/>
                            <TextBlock Text="{Binding FinancialBreakdown}" Style="{StaticResource SubtitleText}"/>
                            <TextBlock Text="{Binding DetailedStatus}" FontSize="14" Foreground="#757575"/>
                        </StackPanel>

                        <!-- Status Badge -->
                        <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="16" Padding="12,6" VerticalAlignment="Top">
                            <TextBlock Text="{Binding CompletionPercentage, StringFormat='{}{0:F1}% Complete'}"
                                       FontWeight="SemiBold" FontSize="12" Foreground="#2E7D32"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Enhanced Financial Summary Cards -->
                <Grid Grid.Row="1" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Total Commitment Card -->
                    <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCard}">
                        <StackPanel>
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Total Commitment" FontSize="14" FontWeight="Medium" Foreground="#424242"/>
                                <materialDesign:PackIcon Grid.Column="1" Kind="CurrencyUsd" Width="20" Height="20" Foreground="#1565C0"/>
                            </Grid>
                            <TextBlock Text="{Binding AmountUSD, StringFormat=\$\{0:N0\}}" FontSize="24" FontWeight="Bold" Foreground="#1565C0"/>
                            <TextBlock Text="{Binding Type}" FontSize="12" Foreground="#757575" Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Total Invoiced Card -->
                    <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCard}">
                        <StackPanel>
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Total Invoiced" FontSize="14" FontWeight="Medium" Foreground="#424242"/>
                                <materialDesign:PackIcon Grid.Column="1" Kind="FileDocument" Width="20" Height="20" Foreground="#FF8F00"/>
                            </Grid>
                            <TextBlock Text="{Binding TotalInvoicedAmount, StringFormat=\$\{0:N0\}}" FontSize="24" FontWeight="Bold" Foreground="#FF8F00"/>
                            <TextBlock Text="{Binding InvoicesCount, StringFormat=\{0\} invoices}" FontSize="12" Foreground="#757575" Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Total Paid Card -->
                    <materialDesign:Card Grid.Column="2" Style="{StaticResource ModernCard}">
                        <StackPanel>
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Total Paid" FontSize="14" FontWeight="Medium" Foreground="#424242"/>
                                <materialDesign:PackIcon Grid.Column="1" Kind="CheckCircle" Width="20" Height="20" Foreground="#2E7D32"/>
                            </Grid>
                            <TextBlock Text="{Binding TotalPaidAmount, StringFormat=\$\{0:N0\}}" FontSize="24" FontWeight="Bold" Foreground="#2E7D32"/>
                            <TextBlock Text="{Binding FullyPaidInvoicesCount, StringFormat=\{0\} fully paid}" FontSize="12" Foreground="#757575" Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Remaining Card -->
                    <materialDesign:Card Grid.Column="3" Style="{StaticResource ModernCard}">
                        <StackPanel>
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Remaining" FontSize="14" FontWeight="Medium" Foreground="#424242"/>
                                <materialDesign:PackIcon Grid.Column="1" Kind="ClockOutline" Width="20" Height="20" Foreground="#D32F2F"/>
                            </Grid>
                            <TextBlock Text="{Binding RemainingCommitmentAmount, StringFormat=\$\{0:N0\}}" FontSize="24" FontWeight="Bold" Foreground="#D32F2F"/>
                            <TextBlock Text="{Binding RemainingAmountEGP, StringFormat=EGP \{0:N0\}}" FontSize="14" FontWeight="Medium" Foreground="#D32F2F" Margin="0,2,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!-- Enhanced Invoices DataGrid -->
                <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCard}" Padding="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Modern DataGrid Header -->
                        <Border Grid.Row="0" Background="#1565C0" Padding="20,12">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Related Invoices" FontSize="18" FontWeight="SemiBold" Foreground="White"/>
                                <materialDesign:PackIcon Grid.Column="1" Kind="FileDocumentMultiple" Width="24" Height="24" Foreground="White"/>
                            </Grid>
                        </Border>

                        <!-- Enhanced DataGrid -->
                        <DataGrid Grid.Row="1" x:Name="InvoicesDataGrid"
                                  ItemsSource="{Binding Invoices}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  SelectionMode="Single"
                                  RowHeight="45"
                                  FontSize="13"
                                  Margin="20,16,20,20"
                                  Background="White"
                                  BorderThickness="0">

                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#F8F9FA"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="Padding" Value="12,8"/>
                                    <Setter Property="BorderBrush" Value="#E0E0E0"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>

                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Background" Value="White"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F5F5F5"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Invoice #" Binding="{Binding InvoiceNumber}" Width="110"/>
                                <DataGridTextColumn Header="Type" Binding="{Binding TypeDisplay}" Width="120"/>
                                <DataGridTextColumn Header="Amount" Binding="{Binding AmountUSD, StringFormat=\$\{0:N0\}}" Width="100"/>
                                <DataGridTextColumn Header="Paid" Binding="{Binding PaidAmount, StringFormat=\$\{0:N0\}}" Width="100"/>
                                <DataGridTextColumn Header="Remaining" Binding="{Binding RemainingAmount, StringFormat=\$\{0:N0\}}" Width="110"/>

                                <!-- Enhanced Payment Status -->
                                <DataGridTemplateColumn Header="Status" Width="130">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border Background="{Binding PaymentStatus, Converter={StaticResource PaymentStatusBackgroundConverter}}"
                                                    CornerRadius="12" Padding="8,4">
                                                <TextBlock Text="{Binding PaymentStatus}"
                                                           FontSize="11" FontWeight="Medium"
                                                           Foreground="{Binding PaymentStatus, Converter={StaticResource PaymentStatusForegroundConverter}}"
                                                           HorizontalAlignment="Center"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTextColumn Header="Site" Binding="{Binding SiteName}" Width="100"/>
                                <DataGridTextColumn Header="Signature Date" Binding="{Binding SignatureDate, StringFormat=dd/MM/yyyy, TargetNullValue=Not Signed}" Width="130"/>
                                <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </materialDesign:Card>

                <!-- Modern Footer Buttons -->
                <Grid Grid.Row="3" Margin="0,16,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- File Info -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="Attachment" Width="16" Height="16" Foreground="#757575" Margin="0,0,8,0"/>
                        <TextBlock Text="{Binding AttachedFileName, TargetNullValue='No file attached'}"
                                   FontSize="13" Foreground="#757575"/>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="OpenFileButton" Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,12,0" Padding="16,8" Click="OpenFile_Click"
                                Background="{Binding AttachedFileName, Converter={StaticResource CommitmentFileBackgroundConverter}}"
                                Foreground="{Binding AttachedFileName, Converter={StaticResource CommitmentFileForegroundConverter}}"
                                BorderBrush="{Binding AttachedFileName, Converter={StaticResource CommitmentFileBorderConverter}}"
                                ToolTip="Open attached commitment file">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16" Margin="0,0,6,0"/>
                                <TextBlock Text="Open File"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Padding="20,8" Click="Close_Click" Background="#1565C0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Margin="0,0,6,0"/>
                                <TextBlock Text="Close"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Grid>
        </ScrollViewer>
    </Grid>
</Window>
