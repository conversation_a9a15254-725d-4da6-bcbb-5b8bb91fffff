#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace FinancialTracker.Models
{
    public class Commitment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public int ProjectId { get; set; }

        [Required]
        public string Type { get; set; } = "Software Tasks";
        
        private decimal _amountUSD;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountUSD
        {
            get => _amountUSD;
            set => _amountUSD = value;
        }
        
        [Column(TypeName = "decimal(10,4)")]
        public decimal ExchangeRate { get; set; } = 1.0m;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime? StartDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        [MaxLength(500)]
        public string? AttachedFilePath { get; set; }
        
        [MaxLength(100)]
        public string? AttachedFileName { get; set; }
        
        public bool IsActive { get; set; } = true;

        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;

        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Reply> Replies { get; set; } = new List<Reply>();

        // Computed properties for UI
        [NotMapped]
        public int InvoicesCount => Invoices?.Count ?? 0;

        [NotMapped]
        public decimal TotalInvoicedAmount => Invoices?.Sum(i => i.AmountUSD) ?? 0;

        [NotMapped]
        public decimal TotalPaidAmount => Invoices?.Sum(i => i.PaidAmount) ?? 0;

        [NotMapped]
        public decimal TotalUnpaidAmount => Invoices?.Sum(i => i.RemainingAmount) ?? 0;

        [NotMapped]
        public decimal RemainingCommitmentAmount => AmountUSD - TotalInvoicedAmount;

        /// <summary>
        /// المبلغ الإجمالي للارتباط بالجنيه المصري
        /// </summary>
        [NotMapped]
        public decimal AmountEGP => AmountUSD * ExchangeRate;

        /// <summary>
        /// إجمالي المبالغ المفوترة بالجنيه المصري (حسب سعر صرف كل فاتورة)
        /// </summary>
        [NotMapped]
        public decimal TotalInvoicedAmountEGP => Invoices?.Sum(i => i.AmountUSD * i.ExchangeRate) ?? 0;

        /// <summary>
        /// إجمالي المبالغ المدفوعة بالجنيه المصري (حسب سعر صرف كل فاتورة)
        /// </summary>
        [NotMapped]
        public decimal TotalPaidAmountEGP => Invoices?.Sum(i => i.PaidAmount * i.ExchangeRate) ?? 0;

        /// <summary>
        /// المبلغ المتبقي بالدولار (محسوب بناءً على سعر صرف الارتباط)
        /// </summary>
        [NotMapped]
        public decimal RemainingCommitmentAmountCorrected
        {
            get
            {
                if (ExchangeRate <= 0) return RemainingCommitmentAmount; // Fallback to old calculation

                // حساب المبلغ المتبقي بالجنيه المصري
                var remainingEGP = AmountEGP - TotalInvoicedAmountEGP;

                // تحويل المبلغ المتبقي إلى دولار بسعر صرف الارتباط
                return remainingEGP / ExchangeRate;
            }
        }

        /// <summary>
        /// المبلغ المتبقي بالجنيه المصري
        /// </summary>
        [NotMapped]
        public decimal RemainingAmountEGP => AmountEGP - TotalInvoicedAmountEGP;

        [NotMapped]
        public int PaidInvoicesCount => Invoices?.Count(i => i.PaidAmount > 0) ?? 0;

        [NotMapped]
        public int UnpaidInvoicesCount => Invoices?.Count(i => i.RemainingAmount > 0) ?? 0;

        [NotMapped]
        public int FullyPaidInvoicesCount => Invoices?.Count(i => i.PaidAmount >= i.AmountUSD) ?? 0;

        /// <summary>
        /// Financial completion percentage for the commitment
        /// </summary>
        [NotMapped]
        public decimal CompletionPercentage
        {
            get
            {
                if (AmountUSD <= 0) return 0;
                return (TotalPaidAmount / AmountUSD) * 100;
            }
        }

        /// <summary>
        /// Detailed information about commitment status
        /// </summary>
        [NotMapped]
        public string DetailedStatus
        {
            get
            {
                var status = $"Total: {InvoicesCount} invoices";
                if (PaidInvoicesCount > 0)
                    status += $" | Paid: {PaidInvoicesCount}";
                if (UnpaidInvoicesCount > 0)
                    status += $" | Unpaid: {UnpaidInvoicesCount}";
                status += $" | Progress: {CompletionPercentage:F1}%";
                return status;
            }
        }

        /// <summary>
        /// Detailed financial information
        /// </summary>
        [NotMapped]
        public string FinancialBreakdown
        {
            get
            {
                return $"Commitment: ${AmountUSD:N0} | " +
                       $"Invoiced: ${TotalInvoicedAmount:N0} | " +
                       $"Paid: ${TotalPaidAmount:N0} | " +
                       $"Remaining: ${RemainingCommitmentAmount:N0}";
            }
        }

        /// <summary>
        /// معلومات مالية مفصلة مع مراعاة أسعار الصرف المختلفة
        /// </summary>
        [NotMapped]
        public string FinancialBreakdownWithExchangeRates
        {
            get
            {
                return $"Commitment: ${AmountUSD:N0} (EGP {AmountEGP:N0} @ {ExchangeRate:F2}) | " +
                       $"Invoiced: EGP {TotalInvoicedAmountEGP:N0} | " +
                       $"Paid: EGP {TotalPaidAmountEGP:N0} | " +
                       $"Remaining: ${RemainingCommitmentAmountCorrected:N0} (EGP {(AmountEGP - TotalInvoicedAmountEGP):N0})";
            }
        }

        // Property to display type correctly - now shows the original type
        [NotMapped]
        public string TypeDisplay => GetTypeDisplay();

        private string GetTypeDisplay()
        {
            if (string.IsNullOrEmpty(Type))
                return "Other";

            // Return the original type as stored, no conversion
            return Type;
        }

        // Property for categorized type (for filtering and grouping)
        [NotMapped]
        public string TypeCategory => GetTypeCategory();

        private string GetTypeCategory()
        {
            if (string.IsNullOrEmpty(Type))
                return "Other";

            // If type is a number, convert it to text
            if (int.TryParse(Type, out int typeNumber))
            {
                return typeNumber switch
                {
                    1 => "Equipment",
                    2 => "Equipment",
                    3 => "Services",
                    _ => "Other"
                };
            }

            // Check if type contains service-related keywords
            var lowerType = Type.ToLower();
            if (lowerType.Contains("service") || lowerType.Contains("services") ||
                lowerType.Contains("maintenance") || lowerType.Contains("support"))
            {
                return "Services";
            }

            // Check for specific hardware types (more specific checks first)
            if (lowerType.Contains("hardware equipment") || lowerType.Contains("hw equipment"))
            {
                return "Hardware";
            }

            // Check for specific software types
            if (lowerType.Contains("software equipment") || lowerType.Contains("sw equipment"))
            {
                return "Software";
            }

            // Check for spare parts
            if (lowerType.Contains("spare") || lowerType.Contains("parts"))
            {
                return "Spare Parts";
            }

            // Check if type contains task/software/hardware/equipment keywords (general equipment)
            if (lowerType.Contains("task") || lowerType.Contains("software") ||
                lowerType.Contains("hardware") || lowerType.Contains("tasks") ||
                lowerType.Contains("programming") || lowerType.Contains("equipment"))
            {
                return "Equipment";
            }

            // Default mapping for common values
            return lowerType switch
            {
                "task" => "Equipment",
                "tasks" => "Equipment",
                "software tasks" => "Equipment",
                "hardware tasks" => "Equipment",
                "service" => "Services",
                "services" => "Services",
                "programming" => "Equipment",
                "equipment" => "Equipment",
                _ => "Equipment" // Default to Equipment if unclear
            };
        }
    }
}
