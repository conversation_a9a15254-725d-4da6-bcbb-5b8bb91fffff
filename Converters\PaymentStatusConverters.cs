using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace FinancialTracker.Converters
{
    /// <summary>
    /// Converter to determine payment status color based on paid amount vs total amount
    /// </summary>
    public class PaymentStatusToColorConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || values[0] == null || values[1] == null)
                return Colors.Gray;

            if (values[0] is decimal paidAmount && values[1] is decimal totalAmount)
            {
                if (totalAmount <= 0)
                    return Colors.Gray;

                if (paidAmount >= totalAmount)
                    return Colors.Green;      // Fully paid
                else if (paidAmount > 0)
                    return Colors.Orange;     // Partially paid
                else
                    return Colors.Red;        // Unpaid
            }

            return Colors.Gray;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter to determine payment status text based on paid amount vs total amount
    /// </summary>
    public class PaymentStatusConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || values[0] == null || values[1] == null)
                return "Unknown";

            if (values[0] is decimal paidAmount && values[1] is decimal totalAmount)
            {
                if (totalAmount <= 0)
                    return "Invalid";

                if (paidAmount >= totalAmount)
                    return "Fully Paid";
                else if (paidAmount > 0)
                    return "Partially Paid";
                else
                    return "Unpaid";
            }

            return "Unknown";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter to show/hide elements based on null values
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value != null ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for payment status background color
    /// </summary>
    public class PaymentStatusBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "Fully Paid" => new SolidColorBrush(Color.FromRgb(232, 245, 233)), // Light green
                    "Partially Paid" => new SolidColorBrush(Color.FromRgb(255, 243, 224)), // Light orange
                    "Unpaid" => new SolidColorBrush(Color.FromRgb(255, 235, 238)), // Light red
                    _ => new SolidColorBrush(Color.FromRgb(245, 245, 245)) // Light gray
                };
            }
            return new SolidColorBrush(Color.FromRgb(245, 245, 245));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for payment status foreground color
    /// </summary>
    public class PaymentStatusForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "Fully Paid" => new SolidColorBrush(Color.FromRgb(46, 125, 50)), // Dark green
                    "Partially Paid" => new SolidColorBrush(Color.FromRgb(255, 143, 0)), // Dark orange
                    "Unpaid" => new SolidColorBrush(Color.FromRgb(211, 47, 47)), // Dark red
                    _ => new SolidColorBrush(Color.FromRgb(117, 117, 117)) // Dark gray
                };
            }
            return new SolidColorBrush(Color.FromRgb(117, 117, 117));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
