using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace FinancialTracker.Views
{
    public partial class CommitmentDetailsDialog : Window
    {
        private Commitment _commitment;

        public CommitmentDetailsDialog(Commitment commitment)
        {
            InitializeComponent();
            _commitment = commitment;
            DataContext = _commitment;
            
            Title = $"Commitment Details - {_commitment.Title}";
            LoadCommitmentDetails();
        }

        private async void LoadCommitmentDetails()
        {
            try
            {
                // Reload commitment with all related data
                var commitments = await App.DataService.GetCommitmentsByProjectAsync(_commitment.ProjectId);
                var updatedCommitment = commitments.FirstOrDefault(c => c.Id == _commitment.Id);
                if (updatedCommitment != null)
                {
                    _commitment = updatedCommitment;
                }
                
                if (_commitment != null)
                {
                    DataContext = _commitment;
                    InvoicesDataGrid.ItemsSource = _commitment.Invoices?.OrderByDescending(i => i.CreatedDate);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading commitment details: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void OpenFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_commitment != null && !string.IsNullOrEmpty(_commitment.AttachedFilePath))
                {
                    string fullPath = App.FileService.GetFullPath(_commitment.AttachedFilePath);
                    if (File.Exists(fullPath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = fullPath,
                            UseShellExecute = true
                        });
                    }
                    else
                    {
                        MessageBox.Show("File not found. The file may have been moved or deleted.", "File Not Found",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("No file attached to this commitment.", "No File",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening file: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
